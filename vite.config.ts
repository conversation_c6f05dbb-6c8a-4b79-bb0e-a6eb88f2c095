import react from '@vitejs/plugin-react';
import path from 'path';
import { ProxyOptions, defineConfig, loadEnv } from 'vite';

// https://vitejs.dev/config/

const plugins = [
	react({
		babel: {
			plugins: [
				[
					'babel-plugin-styled-components',
					{
						displayName: true,
						fileName: false,
						sourceMap: true,
					},
				],
			],
		},
	}),
];

export default defineConfig(({ mode }) => {
	const env = loadEnv(mode, process.cwd(), '');

	const proxyConfig: Record<string, ProxyOptions> =
		env.VITE_USE_PROXY === 'true'
			? {
					'/api-sw': {
						target: env.VITE_SW_API_HOST,
						changeOrigin: true,
						rewrite: (path) => path.replace(/^\/api-sw/, ''),
					},
					'/api': {
						target: env.VITE_SALES_HUB_API_HOST,
						changeOrigin: true,
						rewrite: (path) => path.replace(/^\/api/, ''),
					},
				}
			: {};

	return {
		plugins: [plugins],
		resolve: {
			alias: {
				dnd: '/src/utils/dnd',
				'~assets': path.resolve(__dirname, './src/assets'),
				'~widgets': path.resolve(__dirname, './src/widgets'),
				'~features': path.resolve(__dirname, './src/features'),
				'~layouts': path.resolve(__dirname, './src/app/layouts'),
				'~styles': path.resolve(__dirname, './src/shared/styles'),
				'~pages': path.resolve(__dirname, './src/pages'),
				'~utils': path.resolve(__dirname, './src/shared/utils'),
				'~ui': path.resolve(__dirname, './src/shared/ui'),
				'~types': path.resolve(__dirname, './src/shared/types'),
				'~enums': path.resolve(__dirname, './src/shared/enums'),
				'~hooks': path.resolve(__dirname, './src/shared/hooks'),
				'~api': path.resolve(__dirname, './src/shared/api'),
				'~lib': path.resolve(__dirname, './src/shared/lib'),
				'~constants': path.resolve(__dirname, './src/shared/constants'),
				'~app': path.resolve(__dirname, './src/app'),
				'~': path.resolve(__dirname, './src/app'),
			},
		},
		build: {
			chunkSizeWarningLimit: 1000,
		},
		server: {
			watch: {
				usePolling: true,
			},
			host: true,
			strictPort: true,
			proxy: proxyConfig,
		},
	};
});
