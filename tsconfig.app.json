{
	"compilerOptions": {
		"composite": true,
		"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
		"target": "ES2020",
		"useDefineForClassFields": true,
		"lib": ["ES2020", "DOM", "DOM.Iterable"],
		"module": "ESNext",
		"skipLibCheck": true,
		"baseUrl": "./src",
		"paths": {
			"~assets": ["assets"],
			"~widgets/*": ["widgets/*"],
			"~layouts/*": ["app/layouts/*"],
			"~features/*": ["features/*"],
			"~pages/*": ["pages/*"],
			"~styles/*": ["shared/styles/*"],
			"~ui/*": ["shared/ui/*"],
			"~types/*": ["shared/types/*"],
			"~api/*": ["shared/api/*"],
			"~lib/*": ["shared/lib/*"],
			"~constants/*": ["shared/constants/*"],
			"~utils/*": ["shared/utils/*"],
			"~enums/*": ["shared/enums/*"],
			"~hooks/*": ["shared/hooks/*"],
			"~app/*": ["app/*"]
		},

		/* Bundler mode */
		"moduleResolution": "bundler",
		"allowImportingTsExtensions": true,
		"resolveJsonModule": true,
		"isolatedModules": true,
		"moduleDetection": "force",
		"noEmit": true,
		"jsx": "react-jsx",

		/* Linting */
		"strict": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noFallthroughCasesInSwitch": true
	},
	"include": ["src"]
}
