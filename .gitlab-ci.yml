# Cache modules using lock file
cache:
  key:
    files:
      - yarn.lock
  paths:
    - node_modules/

deploy_static:
  only:
    - master
  script:
    - docker run --rm -v "$PWD":/usr/src/app -w /usr/src/app -u `id -u $USER`:`id -g $USER` --env-file "$ENV_FILE" node:20 bash -c 'yarn install; yarn build;'
    - ansible-playbook -l marc-aws-sw deploy/static.yml
  environment:
    name: master

deploy_static_dev:
  only:
    - development
  script:
    - docker run --rm -v "$PWD":/usr/src/app -w /usr/src/app -u `id -u $USER`:`id -g $USER` --env-file "$ENV_FILE" node:20 bash -c 'yarn install; yarn build;'
    - ansible-playbook -l marc-do-dev-re-payment-hub deploy/static.yml
  environment:
    name: development
