import { FormBorderBox } from './components/FormBorderBox';
import { Spectator__DeleteBtn, Spectator__Footer, Spectator__Wrapper } from './styled';

type PropsT = {
	children: React.ReactNode;
	label: string;
	onClear: () => void;
	onDelete: () => void;
	spectatorLength: number;
};

export const Spectator = ({ children, label, onClear, onDelete, spectatorLength }: PropsT) => {
	return (
		<Spectator__Wrapper>
			<FormBorderBox label={label} onClear={onClear}>
				{children}
			</FormBorderBox>
			{spectatorLength > 1 && (
				<Spectator__Footer>
					{onDelete && (
						<Spectator__DeleteBtn
							onClick={(e) => {
								e.preventDefault();
								onDelete();
							}}
						>
							Delete
						</Spectator__DeleteBtn>
					)}
				</Spectator__Footer>
			)}
		</Spectator__Wrapper>
	);
};
