import { ThemeT } from 'shared/styles/theme';
import styled from 'styled-components';

export const Spectator__Wrapper = styled.div`
	margin: 0 0 48px;
`;
export const Spectator__Footer = styled.div`
	display: flex;
	flex-direction: row-reverse;
	padding: 7px 27px 0;
`;

export const Spectator__DeleteBtn = styled.button<ThemeT>`
	background: transparent;
	border: none;
	cursor: pointer;
	position: relative;
	z-index: 1;
	font-size: 13px;
	font-weight: 700;
	color: ${(props) => props.theme.light.colors.primary.red};
`;
