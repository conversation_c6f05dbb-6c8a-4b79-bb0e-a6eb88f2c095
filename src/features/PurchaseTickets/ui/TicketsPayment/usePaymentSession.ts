// usePaymentSession.tsx
import { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import { ApiDomainError, ErrorCodes } from '~api/errors';
import { OrderService } from '~api/order/order.service';
import { Order } from '~api/order/order.types';
import { PaymentSessionService } from '~api/payment-session/payment-session.service';
import {
	PaymentProviderPaymentType,
	PaymentSession,
} from '~api/payment-session/payment-session.types';
import { usePurchaseTicketsState } from '~features/PurchaseTickets/usePurchaseTicketsState';

import { useOrderSummary } from './useOrderSummary';

type Props = {
	paymentMethodType: PaymentProviderPaymentType;
};

export const usePaymentSession = ({ paymentMethodType }: Props) => {
	const { state } = usePurchaseTicketsState();
	const { totalWithFees, tickets } = useOrderSummary();
	const { posId } = useParams();

	const [sessionState, setSessionState] = useState<{
		paymentSession: PaymentSession | null;
		order: Order;
	} | null>(null);
	const initialCreationRef = useRef<boolean>(false);

	useEffect(() => {
		const createOrUpdateSession = async () => {
			if (!totalWithFees || !state?.purchaserDetails) {
				return;
			}

			if (!initialCreationRef.current) {
				initialCreationRef.current = true;
				// Initial creation of order and payment session
				const order = await OrderService.create({
					pointOfSaleId: posId as string,
					marketplaceFee: totalWithFees.marketplaceFee,
					amount: totalWithFees.subtotal,
					orderItems: tickets.map(({ quantity, productId, spectator }) => ({
						productId,
						quantity,
						holder: { firstName: spectator.firstName, lastName: spectator.lastName },
					})),
					customer: {
						firstName: state.purchaserDetails.firstName,
						lastName: state.purchaserDetails.lastName,
						phone: state.purchaserDetails.phone.internationalNumber,
						country: state.purchaserDetails.phone.countryCode,
						email: state.purchaserDetails.email,
						zip: state.purchaserDetails.zip,
					},
					customFieldResponses: Object.entries(state.additionalQuestions)
						.filter(([, value]) => !!value)
						.map(([customFieldId, value]) => ({
							customFieldId,
							value,
						})),
					entryCode: state.entryCode,
				}).catch((err) => {
					if (err instanceof ApiDomainError && err.code === ErrorCodes.EMAIL_BANNED) {
						toast.error('Purchase declined');
					}
					initialCreationRef.current = false;
					if (
						err instanceof ApiDomainError &&
						err.code === ErrorCodes.CANT_HAVE_FREE_AND_NON_FREE_PRODUCTS
					) {
						toast.error('You can not buy free and non-free tickets in one purchase');
					}
					throw err;
				});

				if (totalWithFees.total === 0) {
					setSessionState({
						order,
						paymentSession: null,
					});
				} else {
					const createdPaymentSession = await PaymentSessionService.create({
						orderId: order.id,
						paymentProviderPaymentType: paymentMethodType,
						total: totalWithFees.total,
						marketplaceFee: totalWithFees.marketplaceFee,
						paymentFee: totalWithFees.paymentFee,
					});

					setSessionState({
						order,
						paymentSession: createdPaymentSession,
					});
				}
			} else if (
				sessionState &&
				sessionState?.paymentSession?.paymentProviderPaymentType !== paymentMethodType
			) {
				if (totalWithFees.total > 0 && sessionState?.paymentSession?.id) {
					// We have an existing session, but the method changed. Update it.
					const updatedPaymentSession = await PaymentSessionService.update(
						sessionState?.paymentSession?.id,
						{
							paymentProviderPaymentType: paymentMethodType,
						},
					);

					setSessionState({
						...sessionState,
						paymentSession: updatedPaymentSession,
					});
				}
			}
		};

		createOrUpdateSession().catch(console.error);
	}, [totalWithFees, tickets, paymentMethodType]);

	return sessionState;
};
