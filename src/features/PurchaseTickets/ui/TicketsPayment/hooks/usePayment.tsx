import { ChangeEvent, useCallback, useState } from 'react';

import checkedIcon from '~assets/payment-checked-icon.svg';

import { CardHolderT } from '../model/types';
import { Payment__CardholderCheckedWrapper } from '../styled';

export const usePayment = () => {
	const [isShowEditOrderModal, setIsShowEditOrderModal] = useState(false);
	const closeEditOrderModal = useCallback(() => {
		setIsShowEditOrderModal(false);
	}, [setIsShowEditOrderModal]);
	const [cardHolder, setCardHolder] = useState<CardHolderT>(null);

	const checked = () => {
		return (
			<Payment__CardholderCheckedWrapper>
				<img src={checkedIcon} alt="checked" />
			</Payment__CardholderCheckedWrapper>
		);
	};
	const onChangeCardHolder = (value: CardHolderT) => {
		setCardHolder(value);
	};

	const [inputValue, setInputValue] = useState('');

	const onCardExpired = (event: ChangeEvent<HTMLInputElement>) => {
		const { value } = event.target;
		const formattedValue = value.replace(/\D/g, '');
		const maxLength = 4;
		const truncatedValue = formattedValue.slice(0, maxLength);
		if (truncatedValue.length >= 2) {
			const month = truncatedValue.slice(0, 2);
			const year = truncatedValue.slice(2, 4);
			setInputValue(`${month}/${year}`);
		} else {
			setInputValue(truncatedValue);
		}
	};
	return {
		isShowEditOrderModal,
		closeEditOrderModal,
		cardHolder,
		onChangeCardHolder,
		inputValue,
		onCardExpired,
		checked,
		setIsShowEditOrderModal,
	};
};
