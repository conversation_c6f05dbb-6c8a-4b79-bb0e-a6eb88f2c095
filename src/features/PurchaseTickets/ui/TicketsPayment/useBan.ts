import { useEffect, useState } from 'react';

import { BanService } from '~api/ban/ban.service';
import { ApiDomainError, ErrorCodes } from '~api/errors';
import { PaymentProviderPaymentType } from '~api/payment-session/payment-session.types';
import { usePaymentHubForm } from '~app/providers/PaymentHubProvider';
import { usePurchaseSettings } from '~features/PurchaseTickets/usePurchaseSettings';
import { usePurchaseTicketsState } from '~features/PurchaseTickets/usePurchaseTicketsState';

type BanState = { banned: boolean; isChecking: boolean };

export const useBan = (
	paymentMethodType: PaymentProviderPaymentType,
	cardDetailsFilled: boolean,
) => {
	const { getFingerprint } = usePaymentHubForm();
	const { state } = usePurchaseTicketsState();
	const { posId } = usePurchaseSettings();

	const [emailBan, setEmailBan] = useState<BanState>({ banned: false, isChecking: false });
	const [fingerprintBan, setFingerprintBan] = useState<BanState>({
		banned: false,
		isChecking: false,
	});

	const isBanError = (err: unknown) => {
		return (
			err instanceof ApiDomainError &&
			[ErrorCodes.FINGERPRINT_BANNED, ErrorCodes.EMAIL_BANNED].includes(err.code)
		);
	};

	const checkEmailBan = async (email: string) => {
		try {
			setEmailBan({ banned: false, isChecking: true });

			await BanService.checkBanByEmail(email, posId);

			console.log('here', 222);

			setEmailBan({ banned: false, isChecking: false });
		} catch (error) {
			console.error(error);
			if (isBanError(error)) {
				setEmailBan({ banned: true, isChecking: false });
			}
		}
	};

	const checkFingerprintBan = async () => {
		try {
			setFingerprintBan({ banned: false, isChecking: true });

			const fingerprint = await getFingerprint();

			if (fingerprint) {
				await BanService.checkBanByFingerprint(fingerprint, posId);
			}

			setFingerprintBan({ banned: false, isChecking: false });
		} catch (error) {
			if (isBanError(error)) {
				setFingerprintBan({ banned: true, isChecking: false });
			}
		}
	};

	useEffect(() => {
		console.log('here', 12313);
		if (state?.purchaserDetails?.email) {
			checkEmailBan(state.purchaserDetails.email);
		}
	}, [state?.purchaserDetails?.email]);

	useEffect(() => {
		console.log('cardDetailsFilled', cardDetailsFilled);
		if (cardDetailsFilled && paymentMethodType === PaymentProviderPaymentType.CARD) {
			checkFingerprintBan();
		}
	}, [cardDetailsFilled]);

	console.log(emailBan.isChecking, fingerprintBan.isChecking);

	return {
		isEmailBanned: emailBan.banned,
		isBanChecking: emailBan.isChecking || fingerprintBan.isChecking,
		isFingerprintBanned: fingerprintBan.banned,
	};
};
