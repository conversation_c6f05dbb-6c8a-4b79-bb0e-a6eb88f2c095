import { ThemeT } from 'shared/styles/theme';
import styled from 'styled-components';

import { Form__Wrapper } from '~styles/shared';
import { StyledStepperWrapper } from '~ui/Stepper/styled';

import { StyledButtonContainer } from '../StepButtons/styled';

export const Payment__Wrapper = styled.div<ThemeT>`
	display: flex;
	justify-content: space-between;
	width: 100%;
	margin: 0 0 76px 0;

	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		flex-direction: column-reverse;
		margin: 0;
		${Form__Wrapper} {
			margin: 0;
		}
	}
`;
export const Payment__Main = styled.main<ThemeT>`
	width: 496px;
	.StripeElement {
		margin-bottom: 24px;
	}
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		width: 100%;
	}
`;
export const Payment__Inner = styled.main<ThemeT>`
	width: 496px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		width: 100%;
	}
	margin: auto;
`;
export const Payment__Aside = styled.aside<ThemeT>`
	width: 342px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		width: 100%;
		margin-bottom: 24px;
	}
`;
export const Payment__CardholderWrapper = styled.div`
	margin: 0 0 30px;
`;

export const Payment__CardholderList = styled.ul`
	list-style: none;
	display: flex;
`;
export const Payment__CardholderItem = styled.li<ThemeT & { $isChecked: boolean }>`
	display: flex;
	align-items: center;
	justify-content: center;
	width: 64px;
	height: 40px;
	border: 1px solid
		${(props) => (props.$isChecked ? props.theme.light.colors.primary.blue : '#dfe3e8')};
	position: relative;
	margin: 0 0 0 13px;
	cursor: pointer;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		margin: 0;
	}
	&:hover {
		border: 1px solid ${(props) => props.theme.light.colors.primary.blue};
	}
	&:first-child {
		margin: 0;
	}
	input[type='radio'] {
		width: 64px;
		height: 40px;
		position: absolute;
		opacity: 0;
		cursor: pointer;
	}
`;
export const Payment__CardholderTitle = styled.p<ThemeT>`
	line-height: 24px;
	font-weight: 600;
	color: ${(props) => props.theme.light.colors.button.outlined};
`;

export const StyledPaymentFormLoaderContainer = styled.div`
	height: 227px;
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
`;

export const Payment__CardholderCheckedWrapper = styled.div<ThemeT>`
	position: absolute;
	left: -3px;
	top: -8px;
	background: #fff;
	width: 16px;
	height: 16px;
	border-radius: 100%;
	border: 1px solid ${(props) => props.theme.light.colors.primary.blue};
	display: flex;
	align-items: center;
	justify-content: center;
`;

export const Payment__CardholderInformationWrapper = styled.div``;
export const Payment__CardIconsWrapper = styled.div`
	display: flex;
	justify-content: end;
	width: 100%;
	img {
		display: inline-block;
		margin: 0 0 0 5px;
		&:first-child {
			margin: 0;
		}
	}
`;
export const Payment__OrderSummaryWrapper = styled.div`
	border-radius: 8px;
	background-color: #fff;
	box-shadow: 0px 20px 40px -4px rgba(145, 158, 171, 0.16);
	padding: 0 16px;
	margin: 0 0 22px;
`;
export const Payment__OrderAsideRow = styled.div`
	display: flex;
	justify-content: space-between;
	margin: 0 0 11px;
`;
export const Payment__OrderAsideTitle = styled.p<ThemeT>`
	line-height: 24px;
	font-weight: 600;
	color: ${(props) => props.theme.light.colors.button.outlined};
`;
export const Payment__OrderSummaryEditBtn = styled.button<ThemeT>`
	border: none;
	background: none;
	cursor: pointer;
	display: flex;
	align-items: center;
	gap: 4px;
	margin: 4px 5px 0 0;
	font-size: 13px;
	color: ${(props) => props.theme.light.colors.primary.blue};
`;
export const Payment__OrderSummaryList = styled.ul`
	list-style: none;
`;
export const Payment__OrderSummaryItem = styled.li`
	display: flex;
	justify-content: space-between;
	padding: 17px 0;
	border-bottom: 0.5px solid #919eab;
	&:last-child {
		border: none;
	}
`;
export const Payment__OrderSummaryName = styled.span<ThemeT>`
	color: ${(props) => props.theme.light.colors.text.secondary};
	font-size: 14px;
	line-height: 22px;
`;
export const Payment__OrderSummaryOrder = styled.span<ThemeT>`
	color: ${(props) => props.theme.light.colors.button.outlined};
	font-size: 14px;
	line-height: 22px;
`;

export const Payment__OrderTotalWrapper = styled.div<ThemeT>`
	border-radius: 8px;
	background-color: #fff;
	box-shadow: 0px 20px 40px -4px ${(props) => props.theme.light.colors.shadow.main};
	padding: 16px 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	margin: 0 0 44px;
`;
export const Payment__OrderTotalPrice = styled.span<ThemeT>`
	font-size: 14px;
	line-height: 22px;
	color: ${(props) => props.theme.light.colors.primary.green};
`;
export const StyledOrderTitle = styled.h4<ThemeT>`
	color: ${(props) => props.theme.light.colors.primary.blue};
	font-size: 24px;
	font-weight: 700;
	line-height: 36px;
	margin-bottom: 21px;
	display: flex;
	justify-content: center;
`;

export const StyledPaymentSpectatorsModalWrapper = styled.div<ThemeT>`
	padding: 87px 61px;
	width: 994px;
	max-height: 90vh;
	border-radius: 8px;
	background-color: #fff;
	box-shadow: 0px 20px 40px -4px ${(props) => props.theme.light.colors.shadow.main};
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	position: fixed;
	overflow-y: auto;
	${StyledStepperWrapper} {
		display: none;
	}
	${StyledButtonContainer} {
		display: none;
	}
	@media screen and (max-width: ${(props) => props.theme.breakpoints.desktopContentWidth}) {
		width: calc(100% - 40px);
	}
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		top: 130px;
		max-height: calc(100vh - 205px);
		transform: translate(-50%, 0);
	}
`;
export const StyledPaymentEmailWrapper = styled.div<ThemeT>`
	border-radius: 8px;
	background-color: #fff;
	box-shadow: 0px 20px 40px -4px ${(props) => props.theme.light.colors.shadow.main};
	padding: 16px 20px;
	width: 100%;
`;
export const StyledPaymentEmailBox = styled.div<ThemeT>`
	display: flex;
	justify-content: space-between;
	align-items: center;
`;
export const StyledPaymentEmail = styled.div<ThemeT>`
	color: ${(props) => props.theme.light.colors.text.disabled};
`;
export const StyledPaymentEmailInputWrapper = styled.div<ThemeT>`
	padding: 16px 0;
`;
