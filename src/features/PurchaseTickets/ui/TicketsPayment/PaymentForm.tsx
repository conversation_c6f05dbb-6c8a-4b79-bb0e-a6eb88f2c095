import { FormChangeEvent } from '@rn-web/payment-hub-sdk';
import { useEffect, useRef } from 'react';

import {
	PaymentProviderPaymentType,
	PaymentSession,
} from '~api/payment-session/payment-session.types';

import { usePaymentHubForm } from '../../../../app/providers/PaymentHubProvider';

export type PaymentHubFormChangeEvent = {
	isValid: boolean;
	paymentMethodType: PaymentProviderPaymentType;
};

type Props = {
	paymentSession: PaymentSession;
	onChange: (event: PaymentHubFormChangeEvent) => void;
};

export const PaymentForm = ({ paymentSession, onChange }: Props) => {
	const containerElementRef = useRef<HTMLDivElement | null>(null);

	const { initForm } = usePaymentHubForm();

	const handlePaymentHubFormChange = (event: FormChangeEvent) => {
		if (
			!Object.values(PaymentProviderPaymentType).includes(
				event.paymentMethodType as PaymentProviderPaymentType,
			)
		) {
			throw new Error(`Invalid payment method type received from payment hub: ${event.paymentMethodType}`);
		}

		onChange({
			isValid: event.isValid,
			paymentMethodType: event.paymentMethodType as PaymentProviderPaymentType,
		});
	};

	useEffect(() => {
		initForm({
			containerElementRef,
			paymentSession,
			onChange: handlePaymentHubFormChange,
		});
	}, [paymentSession, containerElementRef, onChange]);

	return <div ref={containerElementRef}></div>;
};
