import { Modal } from '@mui/material';
import { useState } from 'react';

import editIcon from '~assets/edit-icon.svg';
import { TotalWithFees } from '~features/PurchaseTickets/services/fee-calculator.service';
import { Input } from '~ui/Input';
import { formatCents } from '~utils/number.utils';

import { ManageSpectators } from '../ManageSpectators';
import { SpectatorTicket } from './model/types';
import {
	Payment__CardholderTitle,
	Payment__OrderAsideRow,
	Payment__OrderAsideTitle,
	Payment__OrderSummaryEditBtn,
	Payment__OrderSummaryItem,
	Payment__OrderSummaryList,
	Payment__OrderSummaryName,
	Payment__OrderSummaryOrder,
	Payment__OrderSummaryWrapper,
	Payment__OrderTotalPrice,
	Payment__OrderTotalWrapper,
	StyledOrderTitle,
	StyledPaymentEmail,
	StyledPaymentEmailBox,
	StyledPaymentEmailInputWrapper,
	StyledPaymentEmailWrapper,
	StyledPaymentSpectatorsModalWrapper,
} from './styled';

type Props = {
	tickets: SpectatorTicket[];
	totalWithFees: TotalWithFees | null;
	email: string;
	isShowMerchantFee: boolean;
};

export const OrderSummary = ({ tickets, totalWithFees, email, isShowMerchantFee }: Props) => {
	const [isShowEditOrderModal, setIsShowEditOrderModal] = useState(false);
	const [isEditEmail, setIsEditEmail] = useState(false);

	return (
		<>
			<StyledOrderTitle>Place order</StyledOrderTitle>
			<Payment__OrderSummaryWrapper>
				<Payment__OrderAsideRow>
					<Payment__OrderAsideTitle>Order Summary</Payment__OrderAsideTitle>
					<Payment__OrderSummaryEditBtn onClick={() => setIsShowEditOrderModal(true)}>
						<img src={editIcon} alt="edit" />
						Edit
					</Payment__OrderSummaryEditBtn>
				</Payment__OrderAsideRow>
				<Payment__OrderSummaryList>
					{tickets.map((ticket) => (
						<Payment__OrderSummaryItem>
							<Payment__OrderSummaryName>{ticket.spectator.fullName}</Payment__OrderSummaryName>
							<Payment__OrderSummaryOrder>
								{ticket.quantity} {ticket.ticketName} x ${formatCents(ticket.price)}
							</Payment__OrderSummaryOrder>
						</Payment__OrderSummaryItem>
					))}
					{isShowMerchantFee && (
						<Payment__OrderSummaryItem>
							<Payment__OrderSummaryName>Merchant fee</Payment__OrderSummaryName>
							<Payment__OrderSummaryOrder>
								${formatCents(totalWithFees?.totalCustomerFee)}
							</Payment__OrderSummaryOrder>
						</Payment__OrderSummaryItem>
					)}
				</Payment__OrderSummaryList>
			</Payment__OrderSummaryWrapper>
			<Payment__OrderTotalWrapper>
				<Payment__OrderAsideTitle>Order Total:</Payment__OrderAsideTitle>
				<Payment__OrderTotalPrice>${formatCents(totalWithFees?.total)}</Payment__OrderTotalPrice>
			</Payment__OrderTotalWrapper>
			<StyledPaymentEmailWrapper>
				<Payment__CardholderTitle>Email tickets to:</Payment__CardholderTitle>
				<StyledPaymentEmailBox>
					{!isEditEmail && (
						<>
							<StyledPaymentEmail>{email}</StyledPaymentEmail>
							<Payment__OrderSummaryEditBtn onClick={() => setIsEditEmail(true)}>
								<img src={editIcon} alt="edit" />
								Edit
							</Payment__OrderSummaryEditBtn>
						</>
					)}
				</StyledPaymentEmailBox>
				{isEditEmail && (
					<StyledPaymentEmailInputWrapper>
						<Input fullWidth label="Email" name="email" />
					</StyledPaymentEmailInputWrapper>
				)}
			</StyledPaymentEmailWrapper>
			{isShowEditOrderModal && (
				<Modal open={isShowEditOrderModal} onClose={() => setIsShowEditOrderModal(false)}>
					<StyledPaymentSpectatorsModalWrapper>
						<ManageSpectators onNext={() => setIsShowEditOrderModal(false)} isModal />
					</StyledPaymentSpectatorsModalWrapper>
				</Modal>
			)}
		</>
	);
};
