export type FormInput = {
	name: string;
	surname: string;
	zip: string;
	cardNumber: string;
	cvv: string;
	email: string;
};
export type CardHolderT = 'card' | 'apple' | 'google' | null;

export type SpectatorTicket = {
	productId: string;
	spectator: {
		firstName: string;
		lastName: string;
		fullName: string;
	};
	ticketName: string;
	quantity: number;
	price: number;
	marketplaceFee: number;
};
