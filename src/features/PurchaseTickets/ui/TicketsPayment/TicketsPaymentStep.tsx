import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import ClipLoader from 'react-spinners/ClipLoader';
import { toast } from 'react-toastify';
import { z } from 'zod';

import { FeePayer, PaymentProviderPaymentType } from '~api/payment-session/payment-session.types';
import { usePaymentHubForm } from '~app/providers/PaymentHubProvider';
import { useOrderActions } from '~features/PurchaseTickets/ui/TicketsPayment/useOrderActions.ts';
import { usePurchaseTicketsState } from '~features/PurchaseTickets/usePurchaseTicketsState';
import { FormProvider } from '~lib/react-hook-form/FormProvider';
import { Form__Wrapper } from '~styles/shared';
import { formatCents } from '~utils/number.utils';

import { StepProps } from '..';
import { PurchaseTicketsStepControls } from '../PurchaseTicketsStepControls';
import { OrderSummary } from './OrderSummary';
import { PaymentForm, PaymentHubFormChangeEvent } from './PaymentForm';
import {
	Payment__Aside,
	Payment__CardholderInformationWrapper,
	Payment__CardholderTitle,
	Payment__Inner,
	Payment__Main,
	Payment__Wrapper,
	StyledPaymentFormLoaderContainer,
} from './styled';
import { useBan } from './useBan';
import { useOrderSummary } from './useOrderSummary';
import { usePaymentSession } from './usePaymentSession';

export const PaymentFormSchema = z
	.object({
		hasFilledCardDetails: z.boolean().default(false).optional(),
		isFreePurchase: z.boolean().optional(),
		email: z.string().email('Invalid email address'),
	})
	.refine((data) => data.isFreePurchase || data.hasFilledCardDetails === true, {
		path: ['hasFilledCardDetails'],
		message: 'Card information must be filled',
	});

type PaymentFormData = z.infer<typeof PaymentFormSchema>;

export const TicketsPaymentStep = (props: StepProps) => {
	const navigate = useNavigate();

	const [paymentMethodType, setPaymentMethodType] = useState(PaymentProviderPaymentType.CARD);

	const [confirmingPayment, setConfirmingPayment] = useState<boolean>(false);

	const { state, resetState } = usePurchaseTicketsState();

	const { totalWithFees, tickets, isFreePurchase } = useOrderSummary();

	const methods = useForm<PaymentFormData>({
		resolver: zodResolver(PaymentFormSchema),
		mode: 'all',
		defaultValues: {
			email: state?.purchaserDetails?.email || undefined,
			isFreePurchase: isFreePurchase,
		},
	});

	const {
		formState: { isValid, errors },
		setValue,
	} = methods;

	const sessionState = usePaymentSession({
		paymentMethodType,
	});

	const isShowMerchantFee =
		sessionState?.paymentSession?.marketplaceFeePayer === FeePayer.BUYER &&
		sessionState?.paymentSession?.paymentFeePayer === FeePayer.BUYER;

	const { confirmPayment, formInitialized } = usePaymentHubForm();
	const { markOrderAsPaid } = useOrderActions();

	const isCardDetailsFilled = methods.watch('hasFilledCardDetails');

	const { isEmailBanned, isBanChecking, isFingerprintBanned } = useBan(
		paymentMethodType,
		!!isCardDetailsFilled,
	);

	const onSubmit = async () => {
		try {
			setConfirmingPayment(true);

			if (!isFreePurchase) {
				if (isEmailBanned || isFingerprintBanned) {
					toast.error('Purchase declined');
					return;
				}

				// !! Do not include async call before APPLE PAY payment confirm
				// !! otherwise, APPLE PAY fails with IntegrationError
				await confirmPayment();
			} else if (sessionState?.order) {
				await markOrderAsPaid(sessionState.order.id);
			}

			resetState();

			navigate('payment-success', {
				state: { order: sessionState?.order, customer: state?.purchaserDetails },
			});
		} catch (err) {
			toast.error('Payment failed, please try again');
			throw err;
		} finally {
			setConfirmingPayment(false);
		}
	};

	const handlePaymentHubFormChange = useCallback(
		(event: PaymentHubFormChangeEvent) => {
			setValue('hasFilledCardDetails', event.isValid, { shouldValidate: true });

			if (event.paymentMethodType !== paymentMethodType) {
				setPaymentMethodType(event.paymentMethodType);
			}
		},
		[paymentMethodType, setValue],
	);

	// if payment session changes, we should reset hasFilledCardDetails
	useEffect(() => {
		setValue('hasFilledCardDetails', false, { shouldValidate: true });
	}, [sessionState?.paymentSession]);

	const disablePayButton = !isValid || confirmingPayment || Object.keys(errors).length > 0;

	const payButtonLabel = isFreePurchase ? 'Confirm' : `Pay $${formatCents(totalWithFees?.total)}`;
	return (
		<>
			<FormProvider methods={methods} onSubmit={onSubmit}>
				<PurchaseTicketsStepControls
					activeStep={4}
					onPrev={props.onPrev}
					nextButtonLabel={payButtonLabel}
					disableNext={disablePayButton}
					isLoadingNext={isBanChecking}
				>
					<Payment__Wrapper>
						<Payment__Main>
							<Payment__Inner>
								<Payment__CardholderInformationWrapper>
									<Form__Wrapper $mb={64}>
										{!isFreePurchase && (
											<>
												<Payment__CardholderTitle>Payment Information</Payment__CardholderTitle>
												{sessionState?.paymentSession && (
													<PaymentForm
														paymentSession={sessionState.paymentSession}
														onChange={handlePaymentHubFormChange}
													/>
												)}
												{!formInitialized && (
													<StyledPaymentFormLoaderContainer>
														<ClipLoader />
													</StyledPaymentFormLoaderContainer>
												)}
											</>
										)}
									</Form__Wrapper>
								</Payment__CardholderInformationWrapper>
							</Payment__Inner>
						</Payment__Main>
						<Payment__Aside>
							<OrderSummary
								isShowMerchantFee={isShowMerchantFee}
								tickets={tickets}
								totalWithFees={totalWithFees}
								email={state?.purchaserDetails?.email || ''}
							/>
						</Payment__Aside>
					</Payment__Wrapper>
				</PurchaseTicketsStepControls>
			</FormProvider>
		</>
	);
};
