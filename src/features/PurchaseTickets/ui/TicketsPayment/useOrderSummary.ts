import { sumBy } from 'lodash';
import { useMemo } from 'react';

import { PaymentProviderPaymentType } from '~api/payment-session/payment-session.types';
import { feeCalculatorService } from '~features/PurchaseTickets/services/fee-calculator.service';
import { usePurchaseSettings } from '~features/PurchaseTickets/usePurchaseSettings';
import { usePurchaseTicketsState } from '~features/PurchaseTickets/usePurchaseTicketsState';

import { SpectatorTicket } from './model/types';

export const useOrderSummary = () => {
	const { state } = usePurchaseTicketsState();

	const { products, paymentProviderAccount, pointOfSale } = usePurchaseSettings();

	const spectatorTickets = useMemo(() => {
		const getProductById = (productId: string) => {
			return products.find((p) => p.id === productId);
		};
		const spectators = state?.spectators || [];

		return spectators
			.flatMap((spectator) => {
				return spectator.tickets.map(({ productId, quantity }) => {
					const product = getProductById(productId);

					if (!product) return null;

					return {
						productId,
						spectatorName: `${spectator.firstName} ${spectator.lastName}`,
						spectator: {
							firstName: spectator.firstName,
							lastName: spectator.lastName,
							fullName: `${spectator.firstName} ${spectator.lastName}`,
						},
						quantity,
						price: product.currentPrice,
						marketplaceFee: product.marketplaceFee,
						ticketName: product.label,
					};
				});
			})
			.filter((ticket) => ticket !== null) as SpectatorTicket[];
	}, [products, state?.spectators]);

	const totalWithFees = useMemo(() => {
		const subtotal = sumBy(spectatorTickets, (ticket) => ticket.price * ticket.quantity);
		const marketplaceFee = sumBy(
			spectatorTickets,
			(ticket) => ticket.marketplaceFee * ticket.quantity,
		);

		if (!paymentProviderAccount) {
			return null;
		}

		if (!pointOfSale) {
			return null;
		}

		return feeCalculatorService.calculateTotalWithFees({
			subtotal,
			marketplaceFee,
			marketplaceFeePayer: pointOfSale.marketplaceFeePayer,
			paymentFeePayer: paymentProviderAccount.paymentProviderFeePayer,
			paymentMethodType: PaymentProviderPaymentType.CARD, // todo: pass dynamically
			customFeeFixed: paymentProviderAccount.paymentProviderFeeFixed,
			customFeePercentage: paymentProviderAccount.paymentProviderFeePercentage,
		});
	}, [spectatorTickets, paymentProviderAccount, pointOfSale]);

	return { totalWithFees, tickets: spectatorTickets, isFreePurchase: totalWithFees?.total === 0 };
};
