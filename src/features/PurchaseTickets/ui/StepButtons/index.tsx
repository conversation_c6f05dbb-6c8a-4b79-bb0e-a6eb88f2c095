import { Button } from '~ui/Button';

import { StyledButtonContainer } from './styled';

type Props = {
	onPrev?: () => Promise<void> | void;
	onNext?: () => Promise<void> | void;
	disableNext?: boolean;
	isLoadingNext?: boolean;
	nextButtonLabel?: string;
};

export const StepButtons = (props: Props) => {
	return (
		<StyledButtonContainer>
			<Button onClick={props.onPrev} disabled={!props.onPrev} variant="outlined" label="Previous" />
			<Button
				type="submit"
				onClick={props.onNext}
				variant="contained"
				color="primary"
				disabled={props.disableNext}
				isLoading={props.isLoadingNext}
				label={props.nextButtonLabel || 'Continue'}
			/>
		</StyledButtonContainer>
	);
};
