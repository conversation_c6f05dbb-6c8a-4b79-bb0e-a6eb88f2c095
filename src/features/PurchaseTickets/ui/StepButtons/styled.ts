import styled from 'styled-components';
import { ThemeT } from '~styles/theme';

export const StyledButtonContainer = styled.div<ThemeT>`
	margin-bottom: 50px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		position: fixed;
		margin: 0;
		bottom: 0;
		left: 24px;
		background: ${(props) => props.theme.light.colors.primary.white};
		z-index: 100;
		padding: 16px 0;
		width: calc(100% - 48px);
		display: flex;
		gap: 25px;
		button {
			width: 100%;
		}
	}

	@media screen and (min-width: ${(props) => props.theme.breakpoints.tablet}) {
		display: flex;
		justify-content: flex-end;
		gap: 24px;
		button {
			width: 204px;
			height: 48px;
		}
	}
`;
