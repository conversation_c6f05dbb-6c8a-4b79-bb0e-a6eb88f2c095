import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import plusIcon from '~assets/plus.svg';
import { TicketSelectionErrors } from '~enums/ticket-selection-errors';
import { Spectator } from '~features/Spectator';
import { useViewportInfo } from '~hooks/useViewportInfo';
import { FormProvider } from '~lib/react-hook-form/FormProvider';
import { useSpectatorFormErrorsStore } from '~lib/zustand/spectator-form-errors.store';
import { StyledAddNewSpectatorBtn, StyledAddNewSpectatorBtnWrapper } from '~styles/shared';
import { Button } from '~ui/Button';
import { Input } from '~ui/Input';
import { deepEqual } from '~utils/object.utils';
import { NAME_REGEX } from '~utils/regex.utils';

import { StepProps } from '..';
import { usePurchaseTicketsState } from '../../usePurchaseTicketsState';
import { PurchaseTicketsStepControls } from '../PurchaseTicketsStepControls';
import { DeleteSpectatorModal } from './DeleteSpectatorModal';
import { SelectTicketsDropdown } from './SelectTicketsDropdown';
import { ERROR_DISPLAY_DURATION, MAX_SPECTATORS } from './constants';
import {
	StyledSpectatorDropDownWrapper,
	StyledSpectatorInputWrapper,
	StyledSpectatorModalBtnWrapper,
	StyledSpectatorRow,
} from './styled';
import { useSpectatorFormManager } from './useSpectatorFormManager';

export const ManageSpectatorsFormSchema = z.object({
	spectators: z
		.array(
			z.object({
				firstName: z
					.string()
					.regex(
						NAME_REGEX,
						'Name must have at least 2 letters and may include only letters, spaces, apostrophes, hyphens, or dots.',
					),
				lastName: z
					.string()
					.regex(
						NAME_REGEX,
						'Name must have at least 2 letters and may include only letters, spaces, apostrophes, hyphens, or dots.',
					),
				tickets: z
					.array(
						z.object({
							productId: z.string(),
							quantity: z.number(),
						}),
					)
					.min(1, 'At least one ticket must be chosen'),
			}),
		)
		.superRefine((spectators, ctx) => {
			const firstNamesMap = new Map<string, number[]>();
			const lastNamesMap = new Map<string, number[]>();

			spectators.forEach((s, index) => {
				const name = s.firstName.toLowerCase().trim() + s.lastName.toLowerCase().trim();
				if (!s.firstName || !s.lastName) return;
				if (!firstNamesMap.has(name)) {
					firstNamesMap.set(name, []);
				}
				if (!lastNamesMap.has(name)) {
					lastNamesMap.set(name, []);
				}
				firstNamesMap.get(name)!.push(index);
				lastNamesMap.get(name)!.push(index);
			});

			for (const indexes of firstNamesMap.values()) {
				if (indexes.length > 1) {
					indexes.forEach((index) => {
						ctx.addIssue({
							code: z.ZodIssueCode.custom,
							message: 'First names must be unique',
							path: [`${index}.firstName`],
						});
					});
				}
			}
			for (const indexes of lastNamesMap.values()) {
				if (indexes.length > 1) {
					indexes.forEach((index) => {
						ctx.addIssue({
							code: z.ZodIssueCode.custom,
							message: 'Last names must be unique',
							path: [`${index}.lastName`],
						});
					});
				}
			}
		}),
});

export type ManageSpectatorsFormData = z.infer<typeof ManageSpectatorsFormSchema>;

export type SpectatorFormData = ManageSpectatorsFormData['spectators'][number];

const DEFAULT_SPECTATOR: SpectatorFormData = {
	firstName: '',
	lastName: '',
	tickets: [],
};

type Props = {
	isModal?: boolean;
} & StepProps;

export const ManageSpectators = (props: Props) => {
	const { breakPoint } = useViewportInfo();
	const { state, setSpectators } = usePurchaseTicketsState();
	const { errors, setErrors, clearErrors } = useSpectatorFormErrorsStore();

	const getFirstSpectator = () => {
		return {
			firstName:
				state?.spectators[0]?.firstName ||
				(errors?.spectators?.[0]?.firstName ? '' : state?.purchaserDetails?.firstName),
			lastName:
				state?.spectators[0]?.lastName ||
				(errors?.spectators?.[0]?.lastName ? '' : state?.purchaserDetails?.lastName),
			tickets: state?.spectators[0]?.tickets || [],
		};
	};

	const methods = useForm<ManageSpectatorsFormData>({
		resolver: zodResolver(ManageSpectatorsFormSchema),
		mode: 'onBlur',
		defaultValues: {
			spectators: [getFirstSpectator(), ...(state?.spectators || []).slice(1)],
		},
	});

	useEffect(() => {
		if (errors) {
			Object.entries(errors?.spectators ?? {}).forEach(([indexStr, fields]) => {
				const index = Number(indexStr);
				if (fields) {
					Object.entries(fields).forEach(([fieldName, fieldError]) => {
						methods.setError(`spectators.${index}.${fieldName}` as keyof ManageSpectatorsFormData, {
							type: fieldError?.type,
							message: fieldError?.message,
						});
					});
				}
			});
			clearErrors();
		}
	}, []);

	const { watch } = methods;
	const formValues = watch();

	const [removedSpectatorIndex, setRemovedSpectatorIndex] = useState<number | null>(null);
	const { spectators } = formValues;

	const {
		removeEmptySpectator,
		deleteSpectator,
		customValid,
		setSnackbar,
		isShowSnackbar,
		addNewSpectator,
		resetSpectator,
		getSelectedProductLabel,
	} = useSpectatorFormManager({
		methods,
		removedSpectatorIndex,
		setRemovedSpectatorIndex,
		spectators,
		DEFAULT_SPECTATOR,
	});

	useEffect(() => {
		if (isShowSnackbar) {
			setTimeout(() => {
				setSnackbar({ isShowSnackbar: false });
			}, ERROR_DISPLAY_DURATION);
		}
	}, [isShowSnackbar, setSnackbar]);

	useEffect(() => {
		// https://github.com/orgs/react-hook-form/discussions/11391 useEffect does not trigger when watching spectators array
		if (!deepEqual(formValues.spectators, state?.spectators)) {
			// store it in global state with persist
			if (!props.isModal) {
				setSpectators(formValues.spectators);
			}
		}
	}, [formValues]);

	const onNext = () => {
		setSpectators(spectators);

		props.onNext?.();
	};

	const isHasCustomError =
		methods?.formState?.errors?.spectators
			?.map?.((spectator) => spectator?.firstName)
			.some((err) => err?.type === 'custom') ||
		methods?.formState?.errors?.spectators
			?.map?.((spectator) => spectator?.lastName)
			.some((err) => err?.type === 'custom');

	useEffect(() => {
		if (isHasCustomError) {
			setSnackbar({
				isShowSnackbar: true,
				snackBarMessageType: TicketSelectionErrors.SPECTATOR_NAME_UNIQUE,
				snackBarType: 'error',
			});
		}
	}, [isHasCustomError, setSnackbar]);

	useEffect(() => {
		spectators.forEach((_, index) => {
			methods.trigger(`spectators.${index}.firstName`);
			methods.trigger(`spectators.${index}.lastName`);
		});
		return () => {
			setErrors(methods.formState.errors);
		};
	}, []);

	if (!methods) {
		return <></>;
	}
	const {
		formState: { isValid },
	} = methods;

	const size = breakPoint === 'tabletS' ? 'small' : 'medium';

	return (
		<>
			<FormProvider methods={methods} onSubmit={onNext}>
				<PurchaseTicketsStepControls
					onPrev={props.onPrev}
					activeStep={2}
					disableNext={!isValid}
					key={spectators.length}
				>
					{spectators.map((spectator, index) => {
						const DEFAULT_TEXT = 'Select ticket(s)';

						const { fullText, shortText } =
							spectator.tickets.length < 1
								? { fullText: DEFAULT_TEXT, shortText: DEFAULT_TEXT }
								: getSelectedProductLabel(spectator);

						return (
							<Spectator
								spectatorLength={spectators.length}
								key={`Spectator-${index}`}
								label={`Spectator ${index + 1}`}
								onClear={() => resetSpectator(index)}
								onDelete={() => removeEmptySpectator(index)}
							>
								<StyledSpectatorRow>
									<StyledSpectatorInputWrapper>
										<Input
											className="spectatorInput"
											name={`spectators.${index}.firstName`}
											label="First name"
											size={size}
											onBlur={() => customValid('firstName', index)}
										/>
										<Input
											className="spectatorInput"
											name={`spectators.${index}.lastName`}
											label="Last name"
											size={size}
											onBlur={() => customValid('lastName', index)}
										/>
									</StyledSpectatorInputWrapper>
									<StyledSpectatorDropDownWrapper>
										<SelectTicketsDropdown
											name={`spectators.${index}.tickets`}
											spectatorIndex={index}
											spectator={spectator}
											maxWidth={260}
											primaryText={fullText}
											fallbackText={shortText}
										/>
									</StyledSpectatorDropDownWrapper>
								</StyledSpectatorRow>
							</Spectator>
						);
					})}

					{spectators.length < MAX_SPECTATORS && (
						<StyledAddNewSpectatorBtnWrapper>
							<StyledAddNewSpectatorBtn
								onClick={(e) => {
									e.preventDefault();
									addNewSpectator();
								}}
							>
								<img src={plusIcon} alt="plus" /> Add new Spectator
							</StyledAddNewSpectatorBtn>
						</StyledAddNewSpectatorBtnWrapper>
					)}
				</PurchaseTicketsStepControls>
				{props.isModal && (
					<StyledSpectatorModalBtnWrapper>
						<Button
							variant="contained"
							disabled={!isValid}
							onClick={onNext}
							label="Save"
							fullWidth
						/>
					</StyledSpectatorModalBtnWrapper>
				)}
			</FormProvider>
			{removedSpectatorIndex !== null && (
				<DeleteSpectatorModal
					onClose={() => setRemovedSpectatorIndex(null)}
					spectator={spectators[removedSpectatorIndex]}
					onDelete={deleteSpectator}
				/>
			)}
		</>
	);
};
