import { useFormContext } from 'react-hook-form';

import decreaseImage from '~assets/minus.svg';
import increaseImage from '~assets/plus.svg';
import { TicketSelectionErrors } from '~enums/ticket-selection-errors';
import { useSnackBarStore } from '~lib/zustand/snack-bar.store';
import { formatCents } from '~utils/number.utils';

import { ManageSpectatorsFormData } from '../..';
import { ProductWithQuantity } from '../ProductList';
import { MAX_DAILY_TICKETS, MAX_WEEKEND_TICKETS } from './constants';
import {
	Counter__Button,
	Counter__Calc,
	Counter__Count,
	Counter__Price,
	Counter__Wrapper,
} from './styled';

type Props = {
	product: ProductWithQuantity;
	products: ProductWithQuantity[];
	spectatorIndex: number;
	ticketId: string;
};

export const Counter = ({ product, spectatorIndex, ticketId, products }: Props) => {
	const methods = useFormContext<ManageSpectatorsFormData>();
	const { setSnackbar } = useSnackBarStore();
	const dailyWeekendValidation = () => {
		const updatedSpectators = [...spectators];

		const spectator = updatedSpectators[spectatorIndex];

		const isWeekendTicketSelected = spectator.tickets.some(
			(t) => products.find((p) => p.id === t.productId)?.ticketType === 'weekend',
		);

		const isDailyTicketSelected = spectator.tickets.some(
			(t) => products.find((p) => p.id === t.productId)?.ticketType === 'daily',
		);

		const currentTicket = products.find((p) => p.id === ticketId);

		if (
			(isWeekendTicketSelected && currentTicket?.ticketType === 'daily') ||
			(isDailyTicketSelected && currentTicket?.ticketType === 'weekend')
		) {
			return true;
		}
	};

	const onlyOneWeekendTicketValidation = () => {
		const updatedSpectators = [...spectators];

		const spectator = updatedSpectators[spectatorIndex];

		const isWeekendTicketSelected = spectator.tickets.some(
			(t) => products.find((p) => p.id === t.productId)?.ticketType === 'weekend',
		);

		return isWeekendTicketSelected && spectator.tickets.length === 1;
	};

	const freeTicketsWithNonFreeValidation = () => {
		const updatedSpectators = [...spectators];

		const allTickets = updatedSpectators.reduce(
			(acc, spectator) => {
				return [...acc, ...spectator.tickets];
			},
			[] as { productId: string; quantity: number }[],
		);

		const isFreeTicketSelected = allTickets.some((t) =>
			products.find((p) => t.productId === p.id && Number(p.currentPrice) === 0),
		);

		const isNonFreeTicketSelected = allTickets.some((t) =>
			products.find((p) => t.productId === p.id && Number(p.currentPrice) > 0),
		);

		const currentTicket = products.find((p) => p.id === ticketId);

		if (
			(isFreeTicketSelected && Number(currentTicket?.currentPrice) > 0) ||
			(isNonFreeTicketSelected && Number(currentTicket?.currentPrice) === 0)
		) {
			return true;
		}
	};

	const spectators = methods.watch('spectators');

	const validate = () => {
		if (dailyWeekendValidation()) {
			setSnackbar({
				isShowSnackbar: true,
				snackBarMessageType: TicketSelectionErrors.DAILY_WEEKEND_ERROR,
				snackBarType: 'info',
			});
			return true;
		}

		if (freeTicketsWithNonFreeValidation()) {
			setSnackbar({
				isShowSnackbar: true,
				snackBarMessageType: TicketSelectionErrors.FREE_TICKETS_WITH_NON_FREE_ERROR,
				snackBarType: 'info',
			});
			return true;
		}

		if (onlyOneWeekendTicketValidation()) {
			setSnackbar({
				isShowSnackbar: true,
				snackBarMessageType: TicketSelectionErrors.ONLY_ONE_WEEKEND_TICKET,
				snackBarType: 'info',
			});
			return true;
		}
	};

	const incrementTicket = (index: number, ticketId: string) => {
		const updatedSpectators = [...spectators];

		const spectator = updatedSpectators[index];

		const ticket = spectator.tickets.find(({ productId }) => productId === ticketId);

		if (validate()) {
			return;
		}

		if (!ticket) {
			spectator.tickets.push({ productId: ticketId, quantity: 1 });
		} else {
			if (product.ticketType === 'daily') {
				ticket.quantity = Math.min(MAX_DAILY_TICKETS, ticket.quantity + 1);
			} else {
				ticket.quantity = Math.min(MAX_WEEKEND_TICKETS, ticket.quantity + 1);
			}
		}

		methods.setValue('spectators', updatedSpectators);
		methods.trigger(`spectators.${spectatorIndex}.tickets`);
	};

	const decrementTicket = (index: number, ticketId: string) => {
		const updatedSpectators = [...spectators];

		const spectator = updatedSpectators[index];

		const ticket = spectator.tickets.find(({ productId }) => productId === ticketId);

		if (!ticket) {
			return;
		}

		ticket.quantity -= 1;

		if (ticket.quantity <= 0) {
			spectator.tickets = spectator.tickets.filter(({ quantity }) => quantity > 0);
		}

		methods.setValue('spectators', updatedSpectators);
		methods.trigger(`spectators.${spectatorIndex}.tickets`);
	};

	const productTotal = product.currentPrice * (product.quantity || 1);

	const isCountButtonDisabled = (product: ProductWithQuantity) => {
		const { ticketType, quantity } = product;

		if (dailyWeekendValidation()) {
			return true;
		}
		if (ticketType === 'daily') {
			return quantity === MAX_DAILY_TICKETS;
		}

		return quantity === MAX_WEEKEND_TICKETS;
	};

	return (
		<Counter__Wrapper>
			<Counter__Price $isEmpty={product.quantity === 0}>
				${formatCents(productTotal)}
			</Counter__Price>
			<Counter__Calc>
				<Counter__Button
					type="button"
					onClick={() => decrementTicket(spectatorIndex, ticketId)}
					$disabled={product.quantity === 0}
				>
					<img src={decreaseImage} alt="increase" />
				</Counter__Button>
				<Counter__Count>{product.quantity}</Counter__Count>
				<Counter__Button
					type="button"
					onClick={() => incrementTicket(spectatorIndex, ticketId)}
					$disabled={isCountButtonDisabled(product)}
				>
					<img src={increaseImage} alt="decrease" />
				</Counter__Button>
			</Counter__Calc>
		</Counter__Wrapper>
	);
};
