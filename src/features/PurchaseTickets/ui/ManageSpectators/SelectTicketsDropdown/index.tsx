import { Typography } from '@mui/material';
import { Controller, useFormContext } from 'react-hook-form';

import arrowBottom from '~assets/arrow-bottom.svg';
import arrowTop from '~assets/arrow-top.svg';
import { SmartText } from '~ui/SmartText';

import { SpectatorFormData } from '..';
import { ProductList } from './ProductList';
import {
	CounterDropdown__Arrow,
	CounterDropdown__Main,
	CounterDropdown__Placeholder,
	CounterDropdown__Wrapper,
} from './styled';
import { useCounterDropDown } from './useCounterDropDown';

type Props = {
	spectator: SpectatorFormData;
	spectatorIndex: number;
	name: string;
	maxWidth: number;
	primaryText: string;
	fallbackText: string;
};

export const SelectTicketsDropdown = ({
	spectator,
	spectatorIndex,
	name,
	primaryText,
	fallbackText,
	maxWidth,
}: Props) => {
	const { isOpen, clickHandler } = useCounterDropDown();
	const { control } = useFormContext();

	return (
		<Controller
			name={name}
			control={control}
			render={({ fieldState: { error } }) => (
				<CounterDropdown__Wrapper $isOpen={isOpen}>
					<CounterDropdown__Main onClick={clickHandler} className="counter-dropdown">
						<CounterDropdown__Arrow src={isOpen ? arrowTop : arrowBottom} alt="arrow" />
						<CounterDropdown__Placeholder>
							<SmartText
								primaryText={primaryText}
								fallbackText={fallbackText}
								maxWidth={maxWidth}
							/>
						</CounterDropdown__Placeholder>
					</CounterDropdown__Main>
					{isOpen && (
						<ProductList
							isOpen={isOpen}
							spectatorIndex={spectatorIndex}
							spectator={spectator}
							clickHandler={clickHandler}
						/>
					)}
					{error?.message && (
						<Typography mt={0.375} marginX={1.75} color="error" variant="caption">
							{error.message}
						</Typography>
					)}
				</CounterDropdown__Wrapper>
			)}
		/>
	);
};

SelectTicketsDropdown.displayName = 'SelectTicketsDropdown';
