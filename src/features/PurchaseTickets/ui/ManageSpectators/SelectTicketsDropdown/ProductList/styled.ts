import { ThemeT } from 'shared/styles/theme';
import styled from 'styled-components';

export const StyledProductListItem = styled.li<ThemeT>`
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 0;
	border-bottom: 1px solid ${(props) => props.theme.light.colors.form.border};
`;
export const StyledProductListDescription = styled.div``;
export const StyledProductListTitle = styled.div<ThemeT>`
	font-size: 16px;
	line-height: 24px;
	font-weight: 600;
	color: ${(props) => props.theme.light.colors.text.primary};
	width: 135px;
`;
export const StyledProductListSubTitle = styled.span<ThemeT>`
	color: ${(props) => props.theme.light.colors.text.secondary};
	font-size: 12px;
	line-height: 16px;
`;

export const StyledProductListMainWrapper = styled.div``;
export const StyledProductListMain = styled.ul<ThemeT &{
	$maxHeight?: number;
	$isShowMoreTickets: boolean;
	$isShowLessTickets: boolean;
}>`
	max-height: ${({ $maxHeight, $isShowMoreTickets }) =>
		($maxHeight || 0) + ($isShowMoreTickets ? 25 : 10)}px;
	width: 322px;
	min-width: 313px;
	position: relative;
	list-style: none;
	margin-top: 8px;
	border: 1px solid ${(props) => props.theme.light.colors.form.border};
	padding: 16px 16px 0;
	border-radius: 8px;
	background: #fff;
	position: relative;
	z-index: 999999;
	background: #fff;
	overflow-y: ${({ $isShowMoreTickets }) => ($isShowMoreTickets ? 'scroll' : 'hidden')};

	& ${StyledProductListItem} {
		&:nth-last-child(2) {
			border-width: ${({ $isShowMoreTickets }) => ($isShowMoreTickets ? '0' : '1px')};
		}
		&:last-child {
			padding: ${({ $isShowMoreTickets }) => ($isShowMoreTickets ? '36px 0 16px' : '16px 0 16px')};
			position: sticky;
			bottom: 0px;
			background: #fff;
			border-bottom: 0;
			border-top: 1px solid ${(props) => props.theme.light.colors.form.border};
		}
		&:first-child {
			border-top: 1px solid ${(props) => props.theme.light.colors.form.border};
		}
	}
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		margin-bottom: 90px;
	}
`;
export const StyledProductListWrapper = styled.div<ThemeT & { $isOpen: boolean }>`
	position: absolute;
	z-index: ${({ $isOpen }) => ($isOpen ? 2 : 0)};
	width: 100%;
`;
export const StyledProductListTotalValue = styled.div<ThemeT>`
	font-size: 16px;
	line-height: 24px;
	color: ${(props) => props.theme.light.colors.primary.blue};
`;
export const StyledMoreTicketsWrapper = styled.div<ThemeT>`
	position: absolute;
	top: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	padding: 2px 0;
	border-bottom: 1px solid ${(props) => props.theme.light.colors.form.border};
	p {
		color: ${(props) => props.theme.light.colors.primary.blue};
		text-align: right;
		font-size: 12px;
		font-weight: 400;
		line-height: 18px;
	}
`;
