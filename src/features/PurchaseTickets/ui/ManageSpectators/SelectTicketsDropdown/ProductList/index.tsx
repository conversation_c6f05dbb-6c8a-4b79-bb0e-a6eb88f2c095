import { motion } from 'framer-motion';
import { useEffect, useMemo, useRef, useState } from 'react';

import { Product } from '~api/product/product.types';
import { usePurchaseSettings } from '~features/PurchaseTickets/usePurchaseSettings';
import { Overlay } from '~ui/Overlay';
import { formatCents } from '~utils/number.utils';

import { SpectatorFormData } from '../..';
import { VISIBLE_TICKET_COUNT } from '../../constants';
import { Counter } from '../Counter';
import arrowBottom from './img/arrow-bottom-icon.svg';
import arrowTop from './img/arrow-top-icon.svg';
import {
	StyledMoreTicketsWrapper,
	StyledProductListDescription,
	StyledProductListItem,
	StyledProductListMain,
	StyledProductListMainWrapper,
	StyledProductListSubTitle,
	StyledProductListTitle,
	StyledProductListTotalValue,
	StyledProductListWrapper,
} from './styled';

type PropsT = {
	isOpen: boolean;
	spectator: SpectatorFormData;
	spectatorIndex: number;
	clickHandler: () => void;
};

const variants = {
	open: { opacity: 1, y: 0, transition: { duration: 0.2 } },
	closed: { opacity: 0, y: '-100%', transition: { duration: 0.2 } },
};

export type ProductWithQuantity = Product & {
	quantity: number;
};

export const ProductList = ({ isOpen, spectator, clickHandler, spectatorIndex }: PropsT) => {
	const { products } = usePurchaseSettings();

	const matchedTickets: ProductWithQuantity[] = useMemo(
		() =>
			products.map((product) => {
				const matchedTicket = spectator.tickets.find(({ productId }) => productId === product.id);

				return {
					...product,
					quantity: matchedTicket?.quantity || 0,
				};
			}),
		[products, spectator],
	);

	const total = matchedTickets.reduce(
		(acc, ticket) => acc + ticket.currentPrice * ticket.quantity,
		0,
	);

	const containerRef = useRef<HTMLUListElement>(null);
	const [maxHeight, setMaxHeight] = useState<number | undefined>(undefined);

	useEffect(() => {
		if (containerRef.current) {
			const liElements = containerRef.current.querySelectorAll('li');
			let totalHeight = 0;
			for (let i = 0; i < Math.min(VISIBLE_TICKET_COUNT, liElements.length); i++) {
				totalHeight += liElements[i].getBoundingClientRect().height;
			}
			setMaxHeight(totalHeight);
		}
	}, [matchedTickets]);

	const [isShowMoreTickets, setIsShowMoreTickets] = useState(false);
	const [isShowLessTickets, setIsShowLessTickets] = useState(false);

	const scrollHandler = (e: React.UIEvent<HTMLUListElement>) => {
		const wrapper = e.currentTarget;
		if (wrapper.scrollTop + wrapper.clientHeight >= wrapper.scrollHeight) {
			setIsShowLessTickets(true);
		}
		if (wrapper.scrollTop === 0) {
			setIsShowLessTickets(false);
		}
	};

	useEffect(() => {
		if (matchedTickets.length >= VISIBLE_TICKET_COUNT) {
			setIsShowMoreTickets(true);
		}
	}, [matchedTickets]);

	return (
		<motion.div animate={isOpen ? 'open' : 'closed'} variants={variants}>
			<StyledProductListWrapper $isOpen={isOpen}>
				<StyledProductListMainWrapper>
					<StyledProductListMain
						ref={containerRef}
						$maxHeight={maxHeight}
						$isShowLessTickets={isShowLessTickets}
						$isShowMoreTickets={isShowMoreTickets}
						onScroll={scrollHandler}
					>
						{matchedTickets
							.sort((a, b) => a.createdAt.localeCompare(b.createdAt))
							.map((ticket) => (
								<StyledProductListItem key={ticket.id}>
									<StyledProductListDescription>
										<StyledProductListTitle>
											{ticket.label}
											{ticket.shortLabel && ` (${ticket.shortLabel})`}
										</StyledProductListTitle>

										{ticket.description && (
											<StyledProductListSubTitle>{ticket.description}</StyledProductListSubTitle>
										)}
									</StyledProductListDescription>
									<Counter
										products={matchedTickets}
										product={ticket}
										spectatorIndex={spectatorIndex}
										ticketId={ticket.id}
									/>
								</StyledProductListItem>
							))}

						<StyledProductListItem>
							{isShowMoreTickets && (
								<StyledMoreTicketsWrapper>
									<p>Scroll for More Tickets</p>
									<img src={isShowLessTickets ? arrowTop : arrowBottom} alt="" />
								</StyledMoreTicketsWrapper>
							)}

							<StyledProductListTitle>Total</StyledProductListTitle>
							<StyledProductListTotalValue>${formatCents(total)}</StyledProductListTotalValue>
						</StyledProductListItem>
					</StyledProductListMain>
				</StyledProductListMainWrapper>
				{isOpen && <Overlay clickHandler={clickHandler} />}
			</StyledProductListWrapper>
		</motion.div>
	);
};
