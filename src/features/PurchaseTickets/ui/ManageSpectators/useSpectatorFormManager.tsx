import { useFieldArray, useForm } from 'react-hook-form';

import { TicketSelectionErrors } from '~enums/ticket-selection-errors';
import { usePurchaseSettings } from '~features/PurchaseTickets/usePurchaseSettings';
import { useSnackBarStore } from '~lib/zustand/snack-bar.store';

import { ManageSpectatorsFormData, SpectatorFormData } from '.';

type Props = {
	methods: ReturnType<typeof useForm<ManageSpectatorsFormData>>;
	removedSpectatorIndex: number | null;
	setRemovedSpectatorIndex: (index: number | null) => void;
	spectators: SpectatorFormData[];
	DEFAULT_SPECTATOR: SpectatorFormData;
};
export const useSpectatorFormManager = ({
	methods,
	removedSpectatorIndex,
	setRemovedSpectatorIndex,
	spectators,
	DEFAULT_SPECTATOR,
}: Props) => {
	const DEFAULT_TEXT = 'Select ticket(s)';
	const { isShowSnackbar, setSnackbar } = useSnackBarStore();
	const { products } = usePurchaseSettings();

	const { append, remove } = useFieldArray({
		control: methods.control,
		name: 'spectators',
	});

	const clearSpectatorErrors = (index: number) => {
		methods.clearErrors(`spectators.${index}.firstName`);
		methods.clearErrors(`spectators.${index}.lastName`);
	};

	const getDuplicateIndices = (index: number) => {
		const currentSpectator = spectators[index];
		const currentFullName = `${currentSpectator.firstName.toLowerCase().trim()}${currentSpectator.lastName.toLowerCase().trim()}`;
		return spectators.reduce((indices, s, i) => {
			if (
				i !== index &&
				`${s.firstName.toLowerCase().trim()}${s.lastName.toLowerCase().trim()}` === currentFullName
			) {
				indices.push(i);
			}
			return indices;
		}, [] as number[]);
	};

	const removeEmptySpectator = (index: number) => {
		const removedSpectator = spectators[index];
		if (removedSpectator.firstName || removedSpectator.lastName) {
			setRemovedSpectatorIndex(index);
			return;
		}
		remove(index);
	};

	const handleSpectatorDeletion = (index: number) => {
		const duplicateIndices = getDuplicateIndices(index);
		if (duplicateIndices.length === 1) {
			duplicateIndices.forEach((i) => {
				if (
					spectators[i].firstName === spectators[index].firstName &&
					spectators[i].lastName === spectators[index].lastName
				) {
					clearSpectatorErrors(i);
				}
			});
		}
		remove(index);
	};

	const deleteSpectator = () => {
		if (removedSpectatorIndex === null) return;

		handleSpectatorDeletion(removedSpectatorIndex);
		setRemovedSpectatorIndex(null);
	};

	const customValid = (fieldName: 'firstName' | 'lastName', index: number) => {
		methods.trigger(`spectators.${index}.${fieldName}`).then((isValid) => {
			if (
				!isValid &&
				methods.getFieldState(`spectators.${index}.${fieldName}`).error?.type !== 'custom'
			) {
				return;
			}

			const duplicateIndices = getDuplicateIndices(index);

			const hasErrorFirstName =
				methods.getFieldState(`spectators.${index}.firstName`).error?.type === 'custom';
			const hasErrorLastName =
				methods.getFieldState(`spectators.${index}.lastName`).error?.type === 'custom';

			hasErrorFirstName && methods.clearErrors(`spectators.${index}.firstName`);
			hasErrorLastName && methods.clearErrors(`spectators.${index}.lastName`);

			if (duplicateIndices.length === 0) {
				setSnackbar({ isShowSnackbar: false });
			}

			if (duplicateIndices.length > 0) {
				methods.setError(`spectators.${index}.firstName`, {
					type: 'custom',
					message: 'First names must be unique',
				});
				methods.setError(`spectators.${index}.lastName`, {
					type: 'custom',
					message: 'Last names must be unique',
				});

				duplicateIndices.forEach((i) => {
					clearSpectatorErrors(i);
					methods.setError(`spectators.${i}.firstName`, {
						type: 'custom',
						message: 'First names must be unique',
					});
					methods.setError(`spectators.${i}.lastName`, {
						type: 'custom',
						message: 'Last names must be unique',
					});
				});
				setSnackbar({
					isShowSnackbar: true,
					snackBarMessageType: TicketSelectionErrors.SPECTATOR_NAME_UNIQUE,
					snackBarType: 'error',
				});
			} else {
				methods.trigger(`spectators.${index}.${fieldName}`);

				spectators.forEach((_, i) => {
					if (i !== index) {
						const prevDuplicate =
							methods.getFieldState(`spectators.${i}.firstName`).error?.type === 'custom' ||
							methods.getFieldState(`spectators.${i}.lastName`).error?.type === 'custom';

						if (prevDuplicate) {
							methods.trigger(`spectators.${i}.firstName`);
							methods.trigger(`spectators.${i}.lastName`);
						}
					}
				});
			}
		});
	};

	const getSelectedProductLabel = (spectator: SpectatorFormData) => {
		const fullLabels: string[] = [];
		const shortLabels: string[] = [];

		spectator.tickets?.forEach(({ productId, quantity }) => {
			const product = products.find((p) => p.id === productId);
			if (!product) return;

			fullLabels.push(
				`${product.label} ${product.shortLabel ? `(${product.shortLabel})` : ''} x${quantity}`,
			);
			shortLabels.push(`${product.shortLabel || product.label} x${quantity}`);
		});

		const fullText = fullLabels.join(', ');
		const shortText = shortLabels.join(', ');

		return spectator.tickets.length < 1
			? { fullText: DEFAULT_TEXT, shortText: DEFAULT_TEXT }
			: { fullText, shortText };
	};

	const addNewSpectator = () => {
		append(DEFAULT_SPECTATOR);
	};

	const resetSpectator = (index: number) => {
		const duplicateIndices = getDuplicateIndices(index);
		if (duplicateIndices.length > 0) {
			duplicateIndices.forEach((i) => {
				clearSpectatorErrors(i);
			});
		}

		methods.setValue(`spectators.${index}`, { ...DEFAULT_SPECTATOR }, { shouldValidate: false });
		methods.clearErrors(`spectators.${index}`);
	};

	return {
		removeEmptySpectator,
		deleteSpectator,
		customValid,
		setSnackbar,
		isShowSnackbar,
		getSelectedProductLabel,
		addNewSpectator,
		resetSpectator,
	};
};
