import styled from 'styled-components';

import { ThemeT } from '~styles/theme';

export const StyledSpectatorRow = styled.div<ThemeT>`
	display: flex;
	gap: 24px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		flex-direction: column;
	}
`;

export const StyledSpectatorInputWrapper = styled.div<ThemeT>`
	display: flex;
	gap: 24px;
	width: 60%;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		width: 100%;
		.MuiInput-input {
			font-size: 4px;
		}
		.MuiFormHelperText-root {
			margin: 3px 0 0 3px;
		}
	}
`;
export const StyledSpectatorDropDownWrapper = styled.div<ThemeT>`
	width: 40%;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		width: 100%;
		.MuiTypography-caption {
			margin: 3px 0 0 3px;
		}
		ul {
			width: 100%;
		}
	}
`;
export const StyledSpectatorModalBtnWrapper = styled.div<ThemeT>`
	max-width: 204px;
	margin-left: auto;
`;
