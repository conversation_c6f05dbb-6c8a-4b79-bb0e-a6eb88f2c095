import { Modal } from '@mui/material';

import { Button } from '~ui/Button';

import { SpectatorFormData } from '..';
import {
	StyledDeleteSpectatorModalButtonWrapper,
	StyledDeleteSpectatorModalDescription,
	StyledDeleteSpectatorModalTitle,
	StyledDeleteSpectatorModalWrapper,
} from './styled';

type Props = {
	onClose: () => void;
	spectator: SpectatorFormData | null;
	onDelete: () => void;
};
export const DeleteSpectatorModal = ({ onClose, spectator, onDelete }: Props) => {
	return (
		<Modal
			open={true}
			onClose={onClose}
			aria-labelledby="modal-modal-title"
			aria-describedby="modal-modal-description"
		>
			<StyledDeleteSpectatorModalWrapper>
				<StyledDeleteSpectatorModalTitle>Deleting Ticket</StyledDeleteSpectatorModalTitle>
				<StyledDeleteSpectatorModalDescription>
					Are you sure you want to delete <br /> ticket for {spectator?.firstName}{' '}
					{spectator?.lastName}?
				</StyledDeleteSpectatorModalDescription>
				<StyledDeleteSpectatorModalButtonWrapper>
					<Button onClick={onClose} variant="outlined" label="No, Cancel" size="small" />

					<Button
						variant="errorContained"
						label="Yes, Delete"
						size="small"
						onClick={(e) => {
							e.preventDefault();
							onDelete();
						}}
					/>
				</StyledDeleteSpectatorModalButtonWrapper>
			</StyledDeleteSpectatorModalWrapper>
		</Modal>
	);
};
