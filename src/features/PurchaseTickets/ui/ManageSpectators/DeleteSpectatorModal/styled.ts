import styled from 'styled-components';

export const StyledDeleteSpectatorModalWrapper = styled.div`
	width: 595px;
	padding: 50px 33px 25px 33px;
	background: #fff;
	border-radius: 10px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	position: fixed;
`;
export const StyledDeleteSpectatorModalTitle = styled.h3`
	font-size: 20px;
	font-weight: 700;
	line-height: 30px;
	text-align: center;
	margin-bottom: 28px;
`;
export const StyledDeleteSpectatorModalDescription = styled.p`
	line-height: 24px;
	text-align: center;
	margin-bottom: 52px;
`;
export const StyledDeleteSpectatorModalButtonWrapper = styled.div`
	display: flex;
	justify-content: center;
	gap: 12px;
`;
