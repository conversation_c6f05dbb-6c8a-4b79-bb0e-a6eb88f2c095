import { zodResolver } from '@hookform/resolvers/zod';
import { Alert } from '@mui/material';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import React, { useEffect, useMemo } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { z } from 'zod';

import { useActiveStepNameStore } from '~lib/zustand/active-step-name.store.store';

import { usePurchaseSettings } from '../usePurchaseSettings';
import { AdditionalQuestionsForm } from './AdditionalQuestionsForm';
import { EnterEntryCode } from './EnterEntryCode';
import { EnterPurchaserDetails, PurchaserDetailsFormSchema } from './EnterPurchasesrDetails';
import { ManageSpectators } from './ManageSpectators';
import { NCSAForm } from './NSCAForm';
import { PurchaseAdmissionInformation } from './PurchaseAdmissionInformation';
import { TicketsPayment } from './TicketsPayment';

export const PurchaseTicketsFormDataSchema = z.object({
	purchaserDetails: PurchaserDetailsFormSchema,
});

export type PurchaseTicketsFormData = z.infer<typeof PurchaseTicketsFormDataSchema>;

export type StepProps = {
	onPrev?: VoidFunction;
	onNext?: VoidFunction;
};

export const PurchaseTickets = () => {
	const methods = useForm<PurchaseTicketsFormData>({
		resolver: zodResolver(PurchaseTicketsFormDataSchema),
	});

	const { hasCustomFields, pointOfSale } = usePurchaseSettings();

	const [currentStep, setCurrentStep] = React.useState(1);

	const steps = useMemo(() => {
		const steps = [
			{ name: 'PurchaseAdmissionInformation', component: PurchaseAdmissionInformation },
		];

		if (pointOfSale?.entryCodeRequired) {
			steps.push({ name: 'EnterEntryCode', component: EnterEntryCode });
		}

		steps.push(
			{ name: 'EnterPurchaserDetails', component: EnterPurchaserDetails },
			{ name: 'ManageSpectators', component: ManageSpectators },
		);

		if (hasCustomFields) {
			steps.push({ name: 'AdditionalQuestionsForm', component: AdditionalQuestionsForm });
		}

		if (pointOfSale?.customSettings.showNcsaAthleteForm) {
			steps.push({ name: 'NCSAForm', component: NCSAForm });
		}

		steps.push({ name: 'TicketsPayment', component: TicketsPayment });

		return steps;
	}, [pointOfSale, hasCustomFields]);

	const { activeStepName } = useActiveStepNameStore();
	useEffect(() => {
		if (activeStepName) {
			const stepName = activeStepName.split('_')[0];
			const activeStepIndex = steps.findIndex((step) => step.name === stepName);
			if (activeStepIndex === -1) return;

			const newStepPosition = activeStepIndex + 1;

			if (newStepPosition < currentStep && newStepPosition !== currentStep) {
				setCurrentStep(newStepPosition);
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [activeStepName]);

	const { published: salesPublished, salesEndDate, salesStartDate } = pointOfSale || {};

	if (!isEmpty(pointOfSale)) {
		if (!salesPublished) {
			return <Alert severity="error">No active sales for event</Alert>;
		}

		if (!dayjs(salesEndDate).isValid() || dayjs().isAfter(salesEndDate)) {
			return <Alert severity="error">Sales ended</Alert>;
		}
		if (!dayjs(salesStartDate).isValid() || dayjs().isBefore(salesStartDate)) {
			return <Alert severity="error">Sales not started</Alert>;
		}
	}

	const isLastStep = currentStep === steps.length;
	const isFirstStep = currentStep === 1;

	const handleNext = () => {
		setCurrentStep((prev) => Math.min(steps.length, prev + 1));
	};
	const handlePrevious = () => setCurrentStep((prev) => Math.max(prev - 1, 0));

	return (
		<FormProvider {...methods}>
			{steps[currentStep - 1] &&
				React.createElement(steps[currentStep - 1].component, {
					onNext: !isLastStep ? handleNext : undefined,
					onPrev: !isFirstStep ? handlePrevious : undefined,
				})}
		</FormProvider>
	);
};
