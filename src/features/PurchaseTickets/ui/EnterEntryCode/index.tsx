import { zodResolver } from '@hookform/resolvers/zod';
import TextField from '@mui/material/TextField';
import { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { z } from 'zod';

import { usePurchaseSettings } from '~features/PurchaseTickets/usePurchaseSettings';
import { usePurchaseTicketsState } from '~features/PurchaseTickets/usePurchaseTicketsState';
import { FormProvider } from '~lib/react-hook-form/FormProvider';

import { StepProps } from '..';
import { StepButtons } from '../StepButtons';
import {
	StyledEntryCodeDescription,
	StyledEntryCodeTextFieldWrapper,
	StyledEntryCodeTitle,
	StyledEntryCodeWrapper,
} from './styled';
import { useEntryCodeValidation } from './useEntryCodeValidation';

const EnterEntryCodeFormSchema = z.object({
	entryCode: z.string().min(1),
});

export type PurchaserDetailsFormData = z.infer<typeof EnterEntryCodeFormSchema>;

export const EnterEntryCode = (props: StepProps) => {
	const { setEntryCode, state } = usePurchaseTicketsState();
	const { posId } = usePurchaseSettings();

	const methods = useForm<PurchaserDetailsFormData>({
		resolver: zodResolver(EnterEntryCodeFormSchema),
		mode: 'onSubmit',
		defaultValues: {
			entryCode: state?.entryCode || undefined,
		},
	});

	const { validate } = useEntryCodeValidation(posId);

	const {
		register,
		formState: { isValid },
		watch,
	} = methods;

	const entryCode = watch('entryCode');

	const onSubmit = useCallback(async () => {
		const { isValid } = await validate(entryCode);

		if (!isValid) {
			return toast('Invalid team code', { type: 'error' });
		}

		setEntryCode(entryCode);

		props.onNext?.();
	}, [entryCode, props, setEntryCode, validate]);

	return (
		<FormProvider methods={methods} onSubmit={onSubmit}>
			<StyledEntryCodeWrapper>
				<StyledEntryCodeTitle>Enter team code</StyledEntryCodeTitle>
				<StyledEntryCodeTextFieldWrapper>
					<TextField
						fullWidth
						variant="outlined"
						label="Team code"
						{...register('entryCode')}
						InputProps={{
							sx: {
								borderRadius: 2,
							},
						}}
					/>
				</StyledEntryCodeTextFieldWrapper>
				<StyledEntryCodeDescription>
					Please contact your club director or team's coach if you do not know your team code.
				</StyledEntryCodeDescription>
			</StyledEntryCodeWrapper>
			<StepButtons onPrev={props.onPrev} disableNext={!isValid} />
		</FormProvider>
	);
};
