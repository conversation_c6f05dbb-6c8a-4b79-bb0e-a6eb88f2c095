import { Box } from '@mui/material';
import { ThemeT } from 'shared/styles/theme';
import styled from 'styled-components';

export const StyledEntryCodeWrapper = styled(Box)`
	max-width: 324px;
	margin: 0 auto 108px;
`;

export const StyledEntryCodeTitle = styled.h6<ThemeT>`
	font-size: 18px;
	line-height: 28px;
	color: ${(props) => props.theme.light.colors.primary.blue};
	margin: 0 0 24px;
	text-align: center;
`;

export const StyledEntryCodeTextFieldWrapper = styled.div`
	margin: 0 0 10px;
`;

export const StyledEntryCodeDescription = styled.p`
	font-size: 14px;
	line-height: 22px;
	word-spacing: -0.2px;
`;
