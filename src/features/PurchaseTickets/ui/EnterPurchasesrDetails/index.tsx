import { zodResolver } from '@hookform/resolvers/zod';
import { Modal } from '@mui/material';
import { postcodeValidator } from 'postcode-validator';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { CountryCode, CountryCodeEnum, CountryCodeKeys } from '~constants/country';
import { FormProvider } from '~lib/react-hook-form/FormProvider';
import { isValidBahamasZip } from '~lib/zod';
import { Button } from '~ui/Button';
import { Input } from '~ui/Input';
import { PhoneInput, PhoneInputSchema } from '~ui/PhoneInput';
import { Select } from '~ui/Select';
import { NAME_REGEX } from '~utils/regex.utils';

import { StepProps } from '..';
import { usePurchaseTicketsState } from '../../usePurchaseTicketsState';
import { PurchaseTicketsStepControls } from '../PurchaseTicketsStepControls';
import {
	StyledDescription,
	StyledEnterPurchaserDetailsListRow,
	StyledEnterPurchaserDetailsRow,
	StyledEnterPurchaserModalItem,
	StyledEnterPurchaserModalList,
	StyledEnterPurchaserModalTitle,
	StyledEnterPurchaserModalWrapper,
	StyledInputContainer,
} from './styled';

const nameValidation = z
	.string()
	.regex(
		NAME_REGEX,
		'Name must have at least 2 letters and may include only letters, spaces, apostrophes, hyphens, or dots.',
	);

export const PurchaserDetailsFormSchema = z
	.object({
		firstName: nameValidation,
		lastName: nameValidation,
		email: z.string().email('Invalid email address'),
		country: z.enum(Object.values(CountryCodeEnum) as [CountryCodeKeys, ...CountryCodeKeys[]]),
		phone: PhoneInputSchema,
		zip: z.string().min(1, 'Zip is required'),
	})
	.superRefine(({ zip, country }, ctx) => {
		if (zip.length === 0) return;

		if (country === 'BS' && !isValidBahamasZip(zip)) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Invalid zip code',
				path: ['zip'],
			});
		}

		if (country !== 'BS' && !postcodeValidator(zip, country)) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Invalid zip code',
				path: ['zip'],
			});
		}
	});
export type PurchaserDetailsFormData = z.infer<typeof PurchaserDetailsFormSchema>;

export const EnterPurchaserDetails = (props: StepProps) => {
	const { state, setPurchaserDetails } = usePurchaseTicketsState();
	const [isShowModal, setIsShowModal] = useState(false);
	const methods = useForm<PurchaserDetailsFormData>({
		resolver: zodResolver(PurchaserDetailsFormSchema),
		mode: 'all',
		defaultValues: {
			...state?.purchaserDetails,
			country: state?.purchaserDetails?.country || CountryCodeEnum.US,
			phone: {
				countryCode: state?.purchaserDetails?.country || CountryCodeEnum.US,
				internationalNumber: state?.purchaserDetails?.phone?.internationalNumber || '',
			},
			zip: state?.purchaserDetails?.zip || '',
		},
	});

	const {
		formState: { isValid },
		watch,
	} = methods;

	const purchaserDetails = watch();
	const onSubmit = useCallback(() => {
		setPurchaserDetails(purchaserDetails);
		setIsShowModal(true);
	}, [purchaserDetails, props]);

	// * if country change then clear zip and phone inputs with errors
	useEffect(() => {
		if (purchaserDetails.country !== state?.purchaserDetails?.country) {
			methods.setValue('zip', '');
			methods.clearErrors('zip');
			methods.setValue('phone', {
				countryCode: purchaserDetails.country,
				internationalNumber: '',
			});
			methods.clearErrors('phone');
		}
	}, [methods, purchaserDetails.country]);

	return (
		<>
			<FormProvider methods={methods} onSubmit={onSubmit}>
				<PurchaseTicketsStepControls onPrev={props.onPrev} disableNext={!isValid} activeStep={1}>
					<StyledEnterPurchaserDetailsListRow>
						<StyledEnterPurchaserDetailsRow>
							<Input placeholder="John" label="First name" name="firstName" />
							<Input placeholder="Smith" label="Last name" name="lastName" />
						</StyledEnterPurchaserDetailsRow>
						<StyledEnterPurchaserDetailsRow>
							<Select placeholder="Country" label="Country" name="country">
								{Object.values(CountryCode).map((country) => (
									<option key={country.stripe} value={country.stripe}>
										{country.text}
									</option>
								))}
							</Select>
							<Input
								placeholder={CountryCode[purchaserDetails.country || CountryCodeEnum.US].zipCode}
								label="Zip Code"
								name="zip"
							/>
						</StyledEnterPurchaserDetailsRow>
						<StyledEnterPurchaserDetailsRow>
							<StyledInputContainer>
								<Input placeholder="<EMAIL>" label="Email" name="email" />
							</StyledInputContainer>
							<StyledInputContainer>
								<PhoneInput name="phone" countryCode={purchaserDetails.country} />
							</StyledInputContainer>
						</StyledEnterPurchaserDetailsRow>
					</StyledEnterPurchaserDetailsListRow>
					<StyledDescription>
						Privacy Statement: Your contact information will NEVER be given to a 3rd party. We will
						ONLY contact you regarding information about this ticket.
					</StyledDescription>
				</PurchaseTicketsStepControls>
			</FormProvider>

			<Modal
				open={isShowModal}
				onClose={() => {}}
				aria-labelledby="modal-modal-title"
				aria-describedby="modal-modal-description"
			>
				<StyledEnterPurchaserModalWrapper>
					<StyledEnterPurchaserModalTitle>Please read</StyledEnterPurchaserModalTitle>
					<StyledEnterPurchaserModalList>
						<StyledEnterPurchaserModalItem>
							An Individual Ticket must be purchased for each spectator.
						</StyledEnterPurchaserModalItem>
						<StyledEnterPurchaserModalItem>
							Name on QR Code Ticket must match government issued photo ID.
						</StyledEnterPurchaserModalItem>
						<StyledEnterPurchaserModalItem>
							Minors without ID must be accompanied by an adult. Weekend tickets cannot contain
							duplicate names.
						</StyledEnterPurchaserModalItem>
					</StyledEnterPurchaserModalList>
					<Button
						variant="contained"
						color="primary"
						size="small"
						onClick={() => {
							setIsShowModal(false);
							props.onNext?.();
						}}
						label="Agree"
					/>
				</StyledEnterPurchaserModalWrapper>
			</Modal>
		</>
	);
};
