import styled from 'styled-components';

import { ThemeT } from '~styles/theme';

export const StyledDescription = styled.div<ThemeT>`
	font-size: 12px;
	line-height: 18px;
	color: ${(props) => props.theme.light.colors.text.secondary};
	margin: 0 0 50px;
`;
export const StyledInputContainer = styled.div<ThemeT>`
	width: 50%;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		width: 100%;
	}
`;

export const StyledEnterPurchaserDetailsListRow = styled.ul``;
export const StyledEnterPurchaserDetailsRow = styled.li<ThemeT>`
	margin: 0 0 40px;
	display: flex;
	justify-content: space-between;
	gap: 16px;

	&:last-child {
		margin: 0 0 13px;
	}

	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		flex-direction: column;
		gap: 32px;
		margin: 0 0 32px;

		&:last-child {
			margin: 0 0 8px;
		}
	}
`;

export const StyledEnterPurchaserModalWrapper = styled.div<ThemeT>`
	padding: 50px 33px;
	width: 604px;
	max-height: 90vh;
	border-radius: 8px;
	background-color: #fff;
	box-shadow: 0px 20px 40px -4px ${(props) => props.theme.light.colors.shadow.main};
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	position: fixed;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.desktopContentWidth}) {
		width: calc(100% - 40px);
	}
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		top: 130px;
		max-height: calc(100vh - 205px);
		transform: translate(-50%, 0);
	}
	button {
		width: 225px;
		height: 45px;
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 0 auto;
	}
`;
export const StyledEnterPurchaserModalTitle = styled.h5<ThemeT>`
	text-align: center;
	font-size: 20px;
	font-weight: 700;
	line-height: 30px;
	margin-bottom: 28px;
`;
export const StyledEnterPurchaserModalList = styled.ul<ThemeT>`
	line-height: 24px;
	margin-bottom: 40px;
`;
export const StyledEnterPurchaserModalItem = styled.li<ThemeT>``;
