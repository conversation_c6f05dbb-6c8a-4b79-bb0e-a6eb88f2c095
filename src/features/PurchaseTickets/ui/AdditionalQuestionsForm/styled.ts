import styled from 'styled-components';

import { ThemeT } from '~styles/theme';

export const Step3Form__Wrapper = styled.div`
	margin: 0 0 16px;
`;
export const Step3Form__Title = styled.p`
	margin: 0 0 16px;
	font-size: 16px;
	font-weight: 700;
	line-height: 24px;
`;
export const StyledStep3FormRow = styled.div`
	margin: 0 0 16px;
`;
export const StyledStep3FormRequired = styled.span<ThemeT>`
	&:after {
		content: '*';
		color: ${(props) => props.theme.light.colors.primary.red};
	}
`;
