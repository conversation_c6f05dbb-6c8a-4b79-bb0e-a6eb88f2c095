import { zodResolver } from '@hookform/resolvers/zod';
import { Typography } from '@mui/material';
import { useCallback, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { CustomFieldType } from '~api/custom-field/custom-field.types';
import { usePurchaseSettings } from '~features/PurchaseTickets/usePurchaseSettings';
import { usePurchaseTicketsState } from '~features/PurchaseTickets/usePurchaseTicketsState';
import { FormProvider } from '~lib/react-hook-form/FormProvider';
import { Input } from '~ui/Input';
import { Select } from '~ui/Select';
import { deepEqual } from '~utils/object.utils';

import { StepProps } from '..';
import { PurchaseTicketsStepControls } from '../PurchaseTicketsStepControls';
import {
	Step3Form__Title,
	Step3Form__Wrapper,
	StyledStep3FormRequired,
	StyledStep3FormRow,
} from './styled';

export const AdditionalQuestionsForm = (props: StepProps) => {
	const { state, setAdditionalQuestions } = usePurchaseTicketsState();

	const { customFields } = usePurchaseSettings();

	const formValidatorSchema = useMemo(() => {
		const validationByQuestionId = customFields?.map((question) => {
			const validation = question.required
				? z.string().min(1, 'You must answer this question')
				: z.string().optional();

			return [question.id, validation];
		});

		return z.object({
			...Object.fromEntries(validationByQuestionId || []),
		});
	}, []);

	const methods = useForm({
		resolver: zodResolver(formValidatorSchema),
		mode: 'all',
		defaultValues: {
			...state?.additionalQuestions,
		},
	});

	const additionalQuestions = methods.watch();

	useEffect(() => {
		if (!deepEqual(additionalQuestions, state?.additionalQuestions)) {
			setAdditionalQuestions(additionalQuestions);
		}
	}, [additionalQuestions]);

	const onSubmit = useCallback(
		(data: Record<string, string>) => {
			setAdditionalQuestions(data);

			props.onNext?.();
		},
		[props],
	);

	const isFormValid = methods.formState.isValid;

	return (
		<FormProvider methods={methods} onSubmit={onSubmit}>
			<PurchaseTicketsStepControls onPrev={props.onPrev} activeStep={3} disableNext={!isFormValid}>
				<Step3Form__Wrapper>
					<Step3Form__Title>Additional questions</Step3Form__Title>
					{customFields?.map((question) => {
						const selectedValue = methods.watch(question.id);

						return (
							<>
								<Typography>
									{question.label}
									{question.required ? <StyledStep3FormRequired /> : ''}
								</Typography>

								{question.type === CustomFieldType.TEXT && (
									<StyledStep3FormRow>
										<Input name={question.id} placeholder="Input text">
											{question.label}
										</Input>
									</StyledStep3FormRow>
								)}

								{question.type === CustomFieldType.SELECT && (
									<StyledStep3FormRow>
										<Select name={question.id} key={question.id}>
											{!selectedValue && (
												<option value="" disabled selected key="select_option">
													Select an option
												</option>
											)}
											{question.options?.map((option) => (
												<option key={option.key} value={option.key}>
													{option.value}
												</option>
											))}
										</Select>
									</StyledStep3FormRow>
								)}
							</>
						);
					})}
				</Step3Form__Wrapper>
			</PurchaseTicketsStepControls>
		</FormProvider>
	);
};
