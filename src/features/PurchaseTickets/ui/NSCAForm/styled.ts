import { ThemeT } from 'shared/styles/theme';
import styled from 'styled-components';

export const StyledCollegeProposalTitle = styled.p<ThemeT>`
	font-weight: 600;
	margin: 0 0 16px;
	padding: 0 0 0 22px;
`;

export const StyledCollegeProposalBox = styled.div<ThemeT>`
	border-radius: 8px;
	box-shadow: 0px 20px 40px -4px ${(props) => props.theme.light.colors.shadow.main};
	min-height: 368px;
	padding: 30px;
	margin: 0 0 105px;
`;
export const StyledCollegeProposalContainer = styled.div`
	display: flex;
	justify-content: space-between;
	gap: 25px;
	margin: 0 0 40px;
`;
export const StyledCollegeProposalFooter = styled.div`
	color: #637381;
	font-size: 12px;
	line-height: 18px;
`;
export const StyledCollegeProposalLeftBox = styled.div`
	width: 471px;
	.checkbox-label > span:first-child {
		margin-right: 0;
	}
	.checkbox-label {
		display: flex;
		align-items: center;
		span {
			color: #161c24;
			font-family: 'Public Sans';
			font-size: 14px;
			line-height: 22px;
		}
	}
`;
export const StyledCollegeProposalRightBox = styled.div`
	width: 315px;
`;
export const StyledCollegeProposalList = styled.ul`
	list-style: none;
`;
export const StyledCollegeProposalItem = styled.li`
	margin: 0 0 10px;
	&:last-child {
		margin: 0;
	}
	p {
		color: #212b36;
		line-height: 24px;
	}
`;
export const StyledCollegeProposalReview = styled.div`
	p {
		color: #194ed5;
		font-size: 14px;
		font-weight: 700;
		line-height: 22px;
	}
`;
export const StyledCollegeProposalReviewHeader = styled.div``;
export const StyledCollegeProposalReviewFooter = styled.div`
	text-align: right;
`;
export const StyledCollegeProposalFormWrapper = styled.div``;
export const StyledCollegeProposalFormRow = styled.div`
	display: flex;
	gap: 15px;
	margin: 0 0 15px;
	&:last-child {
		margin: 0;
	}
`;
export const StyledCollegeProposalFormCell = styled.div`
	width: 294px;
`;
export const StyledCollegeProposalWrapper = styled.div<ThemeT>`
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tabletS}) {
		p {
			font-size: 14px;
		}
		${StyledCollegeProposalContainer} {
			flex-direction: column-reverse;
		}
		${StyledCollegeProposalLeftBox} {
			width: 100%;
		}
		${StyledCollegeProposalRightBox} {
			width: 100%;
			img {
				max-width: 100%;
			}
		}
		${StyledCollegeProposalBox} {
			margin: 0;
			padding: 0;
			box-shadow: none;
		}
		${StyledCollegeProposalTitle} {
			padding: 0;
		}
		${StyledCollegeProposalReviewFooter} {
			padding: 0 0 0 50px;
		}
	}
	@media screen and (max-width: ${(props) => props.theme.breakpoints.mobileS}) {
		${StyledCollegeProposalFormRow} {
			flex-direction: column;
		}
		${StyledCollegeProposalFormCell} {
			width: 100%;
		}
	}
`;
