import { zodResolver } from '@hookform/resolvers/zod';
import { Checkbox } from '@mui/material';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { z } from 'zod';

import { EventService } from '~api/event/event.service';
import nscaImage from '~assets/nsca.webp';
import { ConfirmationEnum } from '~enums/confirmation';
import { Gender } from '~enums/gender';
import { usePurchaseTicketsState } from '~features/PurchaseTickets/usePurchaseTicketsState';
import { FormProvider } from '~lib/react-hook-form/FormProvider';
import { Form__Label } from '~styles/shared';
import { NAME_REGEX } from '~utils/regex.utils';

import { StepProps } from '..';
import { PurchaseTicketsStepControls } from '../PurchaseTicketsStepControls';
import { CollegeProposalForm } from './CollegeProposalForm';
import {
	StyledCollegeProposalBox,
	StyledCollegeProposalContainer,
	StyledCollegeProposalFooter,
	StyledCollegeProposalItem,
	StyledCollegeProposalLeftBox,
	StyledCollegeProposalList,
	StyledCollegeProposalReview,
	StyledCollegeProposalReviewFooter,
	StyledCollegeProposalReviewHeader,
	StyledCollegeProposalRightBox,
	StyledCollegeProposalTitle,
	StyledCollegeProposalWrapper,
} from './styled';

const confirmationYesSchema = z.object({
	collegeConfirmation: z.literal(ConfirmationEnum.YES),
	athlete: z.object({
		firstName: z
			.string()
			.regex(
				NAME_REGEX,
				'Name must have at least 2 letters and may include only letters, spaces, apostrophes, hyphens, or dots.',
			),
		lastName: z
			.string()
			.regex(
				NAME_REGEX,
				'Name must have at least 2 letters and may include only letters, spaces, apostrophes, hyphens, or dots.',
			),
		gender: z.nativeEnum(Gender, {
			required_error: 'Gender is required',
			invalid_type_error: 'Gender is required',
		}),
		gradYear: z.coerce.number({ required_error: 'Graduation year is required' }),
	}),
});

const confirmationNoSchema = z.object({
	collegeConfirmation: z.literal(ConfirmationEnum.NO),
});

const formSchema = z.union([confirmationNoSchema, confirmationYesSchema]);

export type NCSAFormData = z.infer<typeof formSchema>;

export const NCSAForm = ({ onNext, onPrev }: StepProps) => {
	const { state, setNCSAFormData } = usePurchaseTicketsState();
	let defaultValues: NCSAFormData | null = null;

	if (state?.NCSAFormData?.collegeConfirmation === ConfirmationEnum.YES) {
		defaultValues = {
			collegeConfirmation: ConfirmationEnum.YES,
			athlete: state.NCSAFormData.athlete,
		};
	} else if (state?.NCSAFormData?.collegeConfirmation === ConfirmationEnum.NO) {
		defaultValues = {
			collegeConfirmation: ConfirmationEnum.NO,
		};
	} else {
		defaultValues = null;
	}

	const methods = useForm<NCSAFormData>({
		resolver: zodResolver(formSchema),
		mode: 'all',
		defaultValues: defaultValues ?? undefined,
	});

	const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
	const { posId } = useParams();

	const { collegeConfirmation } = methods.watch();

	const onSubmit = async (data: NCSAFormData) => {
		if (data.collegeConfirmation === ConfirmationEnum.YES && state?.purchaserDetails) {
			setIsSubmitting(true);

			await EventService.submitNcsa(posId as string, {
				parent_email: state.purchaserDetails.email,
				parent_first: state.purchaserDetails.firstName,
				parent_last: state.purchaserDetails.lastName,
				parent_phone: state.purchaserDetails.phone.internationalNumber,
				zip: state.purchaserDetails.zip,
				athlete_first: data.athlete.firstName,
				athlete_last: data.athlete.lastName,
				athlete_gradyear: data.athlete.gradYear,
				gender: data.athlete.gender,
			}).finally(() => setIsSubmitting(false));
			setNCSAFormData(data);
			onNext?.();
		}
		if (data.collegeConfirmation === ConfirmationEnum.NO) {
			setNCSAFormData(data);
			onNext?.();
		}
	};

	const collegeConfirmationChangeHandler = (confirmation: ConfirmationEnum) => {
		return () => {
			if (collegeConfirmation === confirmation) {
				// resetField does not work, therefore set to empty string
				methods.setValue('collegeConfirmation', '' as ConfirmationEnum, { shouldValidate: true });
			} else {
				methods.setValue('collegeConfirmation', confirmation, { shouldValidate: true });
			}
		};
	};

	const isFormValid = methods.formState.isValid;

	const gender = methods.watch('athlete.gender');
	const gradYear = methods.watch('athlete.gradYear');

	return (
		<FormProvider methods={methods} onSubmit={onSubmit}>
			<PurchaseTicketsStepControls
				activeStep={3}
				onPrev={onPrev}
				disableNext={!isFormValid || isSubmitting}
			>
				<StyledCollegeProposalWrapper>
					<StyledCollegeProposalTitle>
						Does your child want to compete at the college level?
					</StyledCollegeProposalTitle>
					<StyledCollegeProposalBox>
						<StyledCollegeProposalContainer>
							<StyledCollegeProposalLeftBox>
								<StyledCollegeProposalList>
									<StyledCollegeProposalItem>
										<p>
											Find out how NCSA College Recruiting and IMG Academy help high school athletes
											get recruited to play the sports they love in college. Start with a free
											recruiting profile visible to every college coach in the country.
										</p>
									</StyledCollegeProposalItem>
									<StyledCollegeProposalItem>
										<Form__Label
											className="checkbox-label"
											control={<Checkbox name="isCollege" />}
											label="Yes, my child wants to compete in college!"
											onChange={collegeConfirmationChangeHandler(ConfirmationEnum.YES)}
											checked={collegeConfirmation === ConfirmationEnum.YES}
										/>
										<Form__Label
											className="checkbox-label"
											control={<Checkbox name="isCollege" />}
											label="No, thanks"
											onChange={collegeConfirmationChangeHandler(ConfirmationEnum.NO)}
											checked={collegeConfirmation === ConfirmationEnum.NO}
										/>
									</StyledCollegeProposalItem>
									<StyledCollegeProposalItem>
										<StyledCollegeProposalReview>
											<StyledCollegeProposalReviewHeader>
												<p>
													"In my daughters' quest to participate in college athletics, NCSA has
													given them exposure to colleges and coaches they otherwise would not have
													had."
												</p>
											</StyledCollegeProposalReviewHeader>
											<StyledCollegeProposalReviewFooter>
												<p>- Chris Broussard, Fox Sports Analyst & NCSA Parent</p>
											</StyledCollegeProposalReviewFooter>
										</StyledCollegeProposalReview>
									</StyledCollegeProposalItem>
								</StyledCollegeProposalList>
							</StyledCollegeProposalLeftBox>
							<StyledCollegeProposalRightBox>
								<img src={nscaImage} alt="" />
							</StyledCollegeProposalRightBox>
						</StyledCollegeProposalContainer>
						{collegeConfirmation === ConfirmationEnum.YES && (
							<CollegeProposalForm gender={gender} gradYear={gradYear} />
						)}
						<StyledCollegeProposalFooter>
							By selecting "Yes" and submitting, you agree to receive personalized follow-up and
							marketing messages from NCSA by email, phone and automated text. Consent is not a
							condition of purchase. Standard rates apply.
						</StyledCollegeProposalFooter>
					</StyledCollegeProposalBox>
				</StyledCollegeProposalWrapper>
			</PurchaseTicketsStepControls>
		</FormProvider>
	);
};
