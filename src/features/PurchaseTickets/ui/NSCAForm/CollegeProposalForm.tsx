import { Gender } from '~enums/gender';
import { Form__Wrapper } from '~styles/shared';
import { Input } from '~ui/Input';
import { Select } from '~ui/Select';

import {
	StyledCollegeProposalFormCell,
	StyledCollegeProposalFormRow,
	StyledCollegeProposalFormWrapper,
} from './styled';

type Props = {
	gender?: Gender;
	gradYear?: number;
};
export const CollegeProposalForm = ({ gender, gradYear }: Props) => {
	const generateYears = (count = 14) => {
		const startYear = new Date().getFullYear();
		return Array.from({ length: count }, (_, i) => {
			const year = startYear + i;
			return { text: year.toString(), value: year };
		});
	};

	return (
		<StyledCollegeProposalFormWrapper>
			<Form__Wrapper $mb={20}>
				<StyledCollegeProposalFormRow>
					<StyledCollegeProposalFormCell>
						<Input name="athlete.firstName" label="Athlete First Name" />
					</StyledCollegeProposalFormCell>
					<StyledCollegeProposalFormCell>
						<Input name="athlete.lastName" label="Athlete Last Name" />
					</StyledCollegeProposalFormCell>
				</StyledCollegeProposalFormRow>
				<StyledCollegeProposalFormRow>
					<StyledCollegeProposalFormCell>
						<Select label={gender ? 'Gender' : ''} name="athlete.gender">
							{!gender && (
								<option disabled selected key="chooseGender">
									Choose gender
								</option>
							)}
							{Object.values(Gender).map((gender) => (
								<option value={gender} key={gender}>
									{gender}
								</option>
							))}
						</Select>
					</StyledCollegeProposalFormCell>
					<StyledCollegeProposalFormCell>
						<Select label={gradYear ? 'High School Grade Year' : ''} name="athlete.gradYear">
							{!gradYear && (
								<option disabled selected key="chooseGradeYear" value={0}>
									High School Grade Year
								</option>
							)}
							{generateYears().map((year) => (
								<option value={year.value} key={year.value}>
									{year.text}
								</option>
							))}
						</Select>
					</StyledCollegeProposalFormCell>
				</StyledCollegeProposalFormRow>
			</Form__Wrapper>
		</StyledCollegeProposalFormWrapper>
	);
};
