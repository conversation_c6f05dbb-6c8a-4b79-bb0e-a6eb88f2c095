import { Box } from '@mui/material';
import styled from 'styled-components';

import { ButtonStyled } from '~styles/shared';
import { ThemeT } from '~styles/theme';

export const StyledAdmissionInformationWrapper = styled(Box)<ThemeT>`
	max-width: ${(props) => props.theme.breakpoints.desktopContentWidth};
	${ButtonStyled} {
		margin: 0 auto !important;
		display: block;
	}
`;
export const StyledAdmissionInformationTitle = styled(Box)<ThemeT>`
	font-size: 16px;
	line-height: 24px;
	margin-bottom: 24px;
	font-weight: 700;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		font-size: 14px;
		line-height: 22px;
		margin-bottom: 10px;
	}
`;
export const StyledAdmissionInformationDescriptionWrapper = styled(Box)<ThemeT>`
	margin: 0 0 54px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		margin: 0;
	}
	p {
		line-height: 23px;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
			font-size: 14px;
			line-height: 22px;
			margin-bottom: 10px;
		}
		&:first-child {
			margin: 0 0 3px;
		}
	}
	ul {
		padding: 0 0 0 25px;
		margin: 0 0 5px;
	}
	li {
		line-height: 22px;
		margin: 0 0 2px;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
			font-size: 14px;
			line-height: 22px;
			margin-bottom: 10px;
		}
	}
`;
export const StyledAdmissionInformationModalWrapper = styled.div<ThemeT>`
	max-width: ${(props) => props.theme.breakpoints.desktopModalWidth};
	margin: auto;
`;
export const StyledAdmissionInformationModalDescription = styled.p<ThemeT>`
	font-size: 14px;
	line-height: 22px;
	a {
		color: ${(props) => props.theme.light.colors.primary.blue};
		text-decoration: none;
		font-size: 14px;
		line-height: 22px;
		&:hover {
			text-decoration: underline;
		}
	}
`;
export const StyledAdmissionInformationModalList = styled.ul`
	margin: 0 0 16px;
	padding: 0 0 0 25px;
`;
export const StyledAdmissionInformationModalListItem = styled.li<ThemeT>`
	line-height: 22px;
	font-size: 14px;
	a {
		color: ${(props) => props.theme.light.colors.primary.blue};
		text-decoration: none;
		font-size: 14px;
		line-height: 22px;
		&:hover {
			text-decoration: underline;
		}
	}
`;

export const StyledAdmissionInformationContinueButtonWrapper = styled(Box)<ThemeT>`
	max-width: 432px;
	margin: auto;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		position: sticky;
		bottom: 0;
		background: ${(props) => props.theme.light.colors.primary.white};
		z-index: 100;
		padding: 16px 0;
	}
	button {
		width: 100%;
	}
`;
export const StyledAdmissionInformationModalButtonWrapper = styled(Box)<ThemeT>`
	width: 100%;
	display: flex;
	justify-content: center;
	gap: 8px;
	button {
		width: 100%;
		height: 36px;
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.tablet}) {
		justify-content: flex-end;
		margin-top: 50px;
		gap: 24px;
		button {
			max-width: 204px;
			height: 48px;
		}
	}
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		position: sticky;
		bottom: 0;
		background: ${(props) => props.theme.light.colors.primary.white};
		z-index: 100;
		padding: 16px 0;
	}
`;
