import { Checkbox, Modal } from '@mui/material';
import { Link } from 'react-router-dom';

import { usePurchaseSettings } from '~features/PurchaseTickets/usePurchaseSettings';
import { Form__Label, Modal__Wrapper } from '~styles/shared';
import { Button } from '~ui/Button';
import { getStoreLink } from '~utils/store-market.utils';

import { StepProps } from '..';
import { useAdmissionInformation } from './hooks/useAdmissionInformation';
import {
	StyledAdmissionInformationContinueButtonWrapper,
	StyledAdmissionInformationDescriptionWrapper,
	StyledAdmissionInformationModalButtonWrapper,
	StyledAdmissionInformationModalDescription,
	StyledAdmissionInformationModalList,
	StyledAdmissionInformationModalListItem,
	StyledAdmissionInformationModalWrapper,
	StyledAdmissionInformationTitle,
	StyledAdmissionInformationWrapper,
} from './styled';

export const PurchaseAdmissionInformation = (props: StepProps) => {
	const { pointOfSale } = usePurchaseSettings();
	const { isShow, isAgree, setIsAgree, onClose, continueHandler } = useAdmissionInformation({
		nextHandler: props.onNext,
	});

	return (
		<StyledAdmissionInformationWrapper>
			{pointOfSale?.description && (
				<>
					<StyledAdmissionInformationTitle>
						IMPORTANT Admission Information:
					</StyledAdmissionInformationTitle>
					<StyledAdmissionInformationDescriptionWrapper>
						<div dangerouslySetInnerHTML={{ __html: pointOfSale.description }} />
					</StyledAdmissionInformationDescriptionWrapper>
				</>
			)}
			<StyledAdmissionInformationContinueButtonWrapper>
				<Button onClick={continueHandler} variant="contained" color="primary" label="Continue" />
			</StyledAdmissionInformationContinueButtonWrapper>

			<Modal
				open={isShow}
				onClose={onClose}
				aria-labelledby="modal-modal-title"
				aria-describedby="modal-modal-description"
			>
				<Modal__Wrapper>
					<StyledAdmissionInformationModalWrapper>
						<StyledAdmissionInformationModalDescription>
							Each pass allows for ONE Spectator to attend the event for the day or days specified.
							After purchase, you will receive a QR Code ticket by email to the event. Show this QR
							Code and an id (drivers license, state id, etc) to enter the event. Alternatively, we
							encourage you to use the{' '}
							<Link target="_blank" to={getStoreLink()}>
								SportWrench App
							</Link>{' '}
							to manage your tickets and ID verification.
						</StyledAdmissionInformationModalDescription>
						<StyledAdmissionInformationModalList>
							<StyledAdmissionInformationModalListItem>
								If you have a ticketing issue, please communicate with the event immediately
							</StyledAdmissionInformationModalListItem>
							<StyledAdmissionInformationModalListItem>
								Refunds for unused passes will be issued on request.{' '}
								<Link to={'/'}>Refund Policy</Link>
							</StyledAdmissionInformationModalListItem>
							<StyledAdmissionInformationModalListItem>
								If you dispute a charge, you will incur a $20 fee and will be banned from all future
								events until this fee is paid. <Link to={'/'}>Dispute Policy</Link>
							</StyledAdmissionInformationModalListItem>
						</StyledAdmissionInformationModalList>
						<Form__Label
							checked={isAgree}
							style={{ paddingLeft: '8px', marginBottom: '16px' }}
							control={<Checkbox onChange={() => setIsAgree(!isAgree)} />}
							label="I agree to the Refund and Dispute Policy"
						/>
						<StyledAdmissionInformationModalButtonWrapper>
							<Button
								onClick={onClose}
								variant="outlined"
								label="Close"
								size="small"
								color="secondary"
							/>

							<Button
								variant="contained"
								color="primary"
								size="small"
								onClick={props.onNext}
								disabled={!isAgree}
								label="Continue"
							/>
						</StyledAdmissionInformationModalButtonWrapper>
					</StyledAdmissionInformationModalWrapper>
				</Modal__Wrapper>
			</Modal>
		</StyledAdmissionInformationWrapper>
	);
};
