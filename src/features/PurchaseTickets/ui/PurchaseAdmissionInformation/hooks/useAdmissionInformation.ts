import { useState } from 'react';

import { useAdmissionConfirmationStore } from '~features/PurchaseTickets/admissionConfirmation.model';

type Props = {
	nextHandler?: VoidFunction;
};
export const useAdmissionInformation = ({ nextHandler }: Props) => {
	const [isShow, setIsShow] = useState(false);
	const { isAgree, setIsAgree } = useAdmissionConfirmationStore((state) => {
		return {
			isAgree: state.iAgree,
			setIsAgree: state.setAgree,
		};
	});
	const onClose = () => setIsShow(false);
	const continueHandler = () => {
		if (isAgree) {
			nextHandler?.();
		} else {
			setIsShow(true);
		}
	};

	return {
		isShow,
		isAgree,
		setIsAgree,
		onClose,
		continueHandler,
	};
};
