import React from 'react';

import { StepButtons } from '../StepButtons';
import { PurchaseTicketsStepper } from './PurchaseTicketsStepper';
import {
	StyledPurchaseTicketsStepControlsContainer,
	StyledPurchaseTicketsStepControlsWrapper,
} from './styled';

type Props = {
	onPrev?: () => Promise<void> | void;
	onNext?: () => Promise<void> | void;
	disableNext?: boolean;
	isLoadingNext?: boolean;
	children: React.ReactNode;
	activeStep: number;
	nextButtonLabel?: string;
};

export const PurchaseTicketsStepControls = (props: Props) => {
	return (
		<StyledPurchaseTicketsStepControlsWrapper>
			<StyledPurchaseTicketsStepControlsContainer>
				<PurchaseTicketsStepper activeStep={props.activeStep} />
				{props.children}
			</StyledPurchaseTicketsStepControlsContainer>
			<StepButtons {...props} />
		</StyledPurchaseTicketsStepControlsWrapper>
	);
};
