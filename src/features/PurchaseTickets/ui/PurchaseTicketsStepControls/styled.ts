import { Box } from '@mui/material';
import styled from 'styled-components';
import { ThemeT } from '~styles/theme';

export const StyledPurchaseTicketsStepControlsContainer = styled(Box)`
	display: flex;
	flex-direction: column;
	margin: 0 auto 30px;
`;

export const StyledPurchaseTicketsStepControlsWrapper = styled.div<ThemeT>`
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		padding-bottom: 100px;
	}
`;
