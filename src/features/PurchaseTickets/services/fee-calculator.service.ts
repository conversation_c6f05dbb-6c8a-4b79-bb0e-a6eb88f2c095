import { FeePayer, PaymentProviderPaymentType } from '~api/payment-session/payment-session.types';

import { paymentProviderFeeCalculatorService } from './payment-provider-fee-calculator.service';

type CalculateTotalsParams = {
	marketplaceFeePayer: FeePayer;
	paymentFeePayer: FeePayer;
	marketplaceFee: number;
	subtotal: number;
	paymentMethodType: PaymentProviderPaymentType;
	customFeeFixed: number;
	customFeePercentage: number;
};

export interface TotalWithFees {
	total: number;
	paymentFee: number;
	marketplaceFee: number;
	totalCustomerFee: number;
	subtotal: number;
}

export class FeeCalculatorService {
	calculateTotalWithFees(params: CalculateTotalsParams): TotalWithFees {
		const { subtotal, marketplaceFee } = params;

		const totalWithoutPaymentFee = this.calculateTotalWithoutPaymentFee({
			subtotal,
			marketplaceFee,
			marketplaceFeePayer: params.marketplaceFeePayer,
		});

		const paymentFee = this.calculatePaymentFee({
			amount: totalWithoutPaymentFee,
			paymentFeePayer: params.paymentFeePayer,
			paymentMethodType: params.paymentMethodType,
			customFeeFixed: params.customFeeFixed,
			customFeePercentage: params.customFeePercentage,
		});

		const total = this.calculateTotalWithPaymentFee({
			subtotal: totalWithoutPaymentFee,
			paymentFee,
			paymentFeePayer: params.paymentFeePayer,
		});

		return {
			total,
			paymentFee,
			marketplaceFee,
			subtotal,
			totalCustomerFee: total - subtotal,
		};
	}

	private calculateTotalWithPaymentFee({
		subtotal,
		paymentFee,
		paymentFeePayer,
	}: {
		paymentFeePayer: FeePayer;
		paymentFee: number;
		subtotal: number;
	}) {
		if (this.buyerPaysPaymentFee(paymentFeePayer)) {
			return subtotal + paymentFee;
		}

		return subtotal;
	}

	private calculateTotalWithoutPaymentFee({
		subtotal,
		marketplaceFee,
		marketplaceFeePayer,
	}: {
		subtotal: number;
		marketplaceFee: number;
		marketplaceFeePayer: FeePayer;
	}) {
		if (this.buyerPaysMarketplaceFee(marketplaceFeePayer)) {
			return subtotal + marketplaceFee;
		}

		return subtotal;
	}

	private buyerPaysMarketplaceFee(marketplaceFeePayer: FeePayer) {
		return marketplaceFeePayer === FeePayer.BUYER;
	}

	private buyerPaysPaymentFee(paymentFeePayer: FeePayer) {
		return paymentFeePayer === FeePayer.BUYER;
	}

	private calculatePaymentFee(params: {
		amount: number;
		paymentMethodType: PaymentProviderPaymentType;
		paymentFeePayer: FeePayer;
		customFeeFixed: number;
		customFeePercentage: number;
	}) {
		const isFeeIncluded = !this.buyerPaysPaymentFee(params.paymentFeePayer);

		const fee = paymentProviderFeeCalculatorService.calculateFee({
			isFeeIncluded,
			amount: params.amount,
			paymentMethodType: params.paymentMethodType,
			customFeeFixed: params.customFeeFixed,
			customFeePercentage: params.customFeePercentage,
		});

		return Math.round(fee);
	}
}

export const feeCalculatorService = new FeeCalculatorService();
