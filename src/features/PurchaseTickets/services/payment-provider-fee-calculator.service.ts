import { PaymentProviderPaymentType } from '~api/payment-session/payment-session.types';

export interface CalculatePaymentProviderFeeParams {
	amount: number;
	paymentMethodType: PaymentProviderPaymentType;
	/**
	 * If true, then fee is calculated using default formula `amount * feePercentage + feeFixed`
	 * else, will be calculated so that customer covers fee `(amount + feeFixed)/(1 - feePercentage)`
	 */
	isFeeIncluded: boolean;
	customFeeFixed: number;
	customFeePercentage: number;
}

export class PaymentProviderFeeCalculatorService {
	// todo: pass custom percentage and fixed
	calculateFee(params: CalculatePaymentProviderFeeParams) {
		if (params.paymentMethodType === PaymentProviderPaymentType.CARD) {
			return this.calculateCardFee(params);
		}

		throw new Error(`Unsupported payment type ${params.paymentMethodType}`);
	}

	private calculateCardFee({
		amount,
		isFeeIncluded,
		customFeeFixed,
		customFeePercentage,
	}: Pick<
		CalculatePaymentProviderFeeParams,
		'amount' | 'isFeeIncluded' | 'customFeeFixed' | 'customFeePercentage'
	>) {
		if (isFeeIncluded) {
			return amount * customFeePercentage + customFeeFixed;
		}

		const amountWithSurcharge = (amount + customFeeFixed) / (1 - customFeePercentage);

		return amountWithSurcharge - amount;
	}
}

export const paymentProviderFeeCalculatorService = new PaymentProviderFeeCalculatorService();
