import { StateCreator, create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

import { createSelectors } from '~lib/zustand';

import { PurchaserDetailsFormData } from './ui/EnterPurchasesrDetails';
import { NCSAFormData } from './ui/NSCAForm';

export type Spectator = {
	firstName: string;
	lastName: string;
	tickets: {
		productId: string;
		quantity: number;
	}[];
};

export type PosPurchaseTicketsState = {
	purchaserDetails: PurchaserDetailsFormData | null;
	spectators: Spectator[];
	additionalQuestions: Record<string, string>;
	entryCode: string | null;
	NCSAFormData: NCSAFormData | null;
};

type State = {
	purchaseTicketsStateMap: {
		[posId: string]: PosPurchaseTicketsState | null;
	};
};

type Actions = {
	setNCSAFormData: (posId: string, NCSAFormData: NCSAFormData) => void;
	setPurchaserDetails: (posId: string, purchaserDetails: PurchaserDetailsFormData) => void;
	setSpectators: (posId: string, spectators: Spectator[]) => void;
	setEntryCode: (posId: string, entryCode: string | null) => void;
	resetState: (posId: string) => void;
	setAdditionalQuestions: (posId: string, additionalQuestions: Record<string, string>) => void;
};

const DEFAULT_INITIAL_STATE: PosPurchaseTicketsState = {
	purchaserDetails: null,
	spectators: [],
	additionalQuestions: {},
	entryCode: null,
	NCSAFormData: null,
};

function mapToDefaultSpectator(purchaserDetails: PurchaserDetailsFormData): Spectator {
	return {
		firstName: purchaserDetails.firstName,
		lastName: purchaserDetails.lastName,
		tickets: [],
	};
}

function createPurchaseTicketsSlice() {
	const purchaseTicketsSlice: StateCreator<
		State & Actions,
		[['zustand/devtools', never], ['zustand/persist', unknown]],
		[],
		State & Actions
	> = (set) => ({
		setNCSAFormData: (posId: string, NCSAFormData: NCSAFormData) =>
			set(({ purchaseTicketsStateMap }) => {
				const state = purchaseTicketsStateMap[posId] || DEFAULT_INITIAL_STATE;

				return {
					purchaseTicketsStateMap: {
						...purchaseTicketsStateMap,
						[posId]: {
							...state,
							NCSAFormData,
						},
					},
				};
			}),
		purchaseTicketsStateMap: {},
		setPurchaserDetails: (posId: string, purchaserDetails: PurchaserDetailsFormData) =>
			set(({ purchaseTicketsStateMap }) => {
				const state = purchaseTicketsStateMap[posId] || DEFAULT_INITIAL_STATE;

				const spectators =
					state.spectators.length > 0
						? [...state.spectators]
						: [mapToDefaultSpectator(purchaserDetails)];

				return {
					purchaseTicketsStateMap: {
						...purchaseTicketsStateMap,
						[posId]: {
							...state,
							purchaserDetails,
							spectators,
						},
					},
				};
			}),

		setEntryCode: (posId: string, entryCode: string | null) =>
			set(({ purchaseTicketsStateMap }) => {
				const state = purchaseTicketsStateMap[posId] || DEFAULT_INITIAL_STATE;

				return {
					purchaseTicketsStateMap: {
						...purchaseTicketsStateMap,
						[posId]: {
							...state,
							entryCode,
						},
					},
				};
			}),

		setSpectators: (posId, spectators) =>
			set(({ purchaseTicketsStateMap }) => {
				const state = purchaseTicketsStateMap[posId];

				if (!state) {
					throw new Error('Cannot set spectators when state is not initialized');
				}

				return {
					purchaseTicketsStateMap: {
						...purchaseTicketsStateMap,
						[posId]: {
							...state,
							spectators,
						},
					},
				};
			}),
		resetState: (posId: string) =>
			set(({ purchaseTicketsStateMap }) => {
				return {
					purchaseTicketsStateMap: {
						...purchaseTicketsStateMap,
						[posId]: null,
					},
				};
			}),
		setAdditionalQuestions: (posId: string, additionalQuestions: Record<string, string>) =>
			set(({ purchaseTicketsStateMap }) => {
				const state = purchaseTicketsStateMap[posId];

				if (!state) {
					throw new Error('Cannot set additional questions when state is not initialized');
				}

				return {
					purchaseTicketsStateMap: {
						...purchaseTicketsStateMap,
						[posId]: {
							...state,
							additionalQuestions,
						},
					},
				};
			}),
	});
	return purchaseTicketsSlice;
}

const slice = createPurchaseTicketsSlice();
const withPersist = persist(slice, { name: 'purchase-tickets' });
const withDevtools = devtools(withPersist, { name: 'Purchase Tickets Service' });
const store = create(withDevtools);

export const usePurchaseTicketsStore = createSelectors(store);
