import { useQuery } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';

import { customFieldQueries } from '~api/custom-field/custom-field.queries';
import { pointOfSaleQueries } from '~api/point-of-sale/point-of-sale.queries';
import { posPaymentProviderAccountQueries } from '~api/pos-payment-provider-account/pos-payment-provider-account.queries';
import { productQueries } from '~api/product/product.queries';

export const usePurchaseSettings = () => {
	const { posId } = useParams();

	if (!posId) {
		throw new Error('POS ID must be in params');
	}

	const { data: pointOfSale, isLoading: isLoadingPointOfSale } = useQuery(
		pointOfSaleQueries.getById(posId),
	);

	const { data: customFields, isLoading: isLoadingCustomFields } = useQuery(
		customFieldQueries.listByPosId(posId),
	);

	const { data: paymentProviderAccount, isLoading: isLoadingPaymentProviderAccount } = useQuery(
		posPaymentProviderAccountQueries.getByPosId(posId as string),
	);

	const { data: products = [] } = useQuery(productQueries.listByPosId(posId as string));

	return {
		hasCustomFields: !!customFields?.length,
		customFields: customFields?.slice().sort((a, b) => a.order - b.order),
		pointOfSale,
		paymentProviderAccount,
		products,
		posId,
		isLoadingPointOfSale,
		isLoadingCustomFields,
		isLoadingPaymentProviderAccount,
	};
};
