import { useParams } from 'react-router-dom';

import { Spectator, usePurchaseTicketsStore } from './purchase-tickets.model';
import { PurchaserDetailsFormData } from './ui/EnterPurchasesrDetails';
import { NCSAFormData } from './ui/NSCAForm';

export const usePurchaseTicketsState = () => {
	const { posId } = useParams();

	if (!posId) {
		throw new Error('Param posId is not available');
	}

	const {
		purchaseTicketsStateMap,
		setNCSAFormData,
		setAdditionalQuestions,
		setPurchaserDetails,
		setSpectators,
		resetState,
		setEntryCode,
	} = usePurchaseTicketsStore();

	return {
		state: purchaseTicketsStateMap[posId] || null,
		setNCSAFormData: (NCSAFormData: NCSAFormData) => setNCSAFormData(posId, NCSAFormData),
		setPurchaserDetails: (purchaserDetails: PurchaserDetailsFormData) =>
			setPurchaserDetails(posId, purchaserDetails),
		setSpectators: (spectators: Spectator[]) => setSpectators(posId, spectators),
		setAdditionalQuestions: (additionalQuestions: Record<string, string>) =>
			setAdditionalQuestions(posId, additionalQuestions),
		resetState: () => resetState(posId),
		setEntryCode: (teamCode: string | null) => setEntryCode(posId, teamCode),
	};
};
