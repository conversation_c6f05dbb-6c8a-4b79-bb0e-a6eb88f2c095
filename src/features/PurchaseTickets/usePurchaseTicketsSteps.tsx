import { usePurchaseSettings } from './usePurchaseSettings';

export const usePurchaseTicketsSteps = () => {
	const { hasCustomFields, pointOfSale, isLoadingCustomFields, isLoadingPointOfSale } =
		usePurchaseSettings();

	const isShowStepOther = pointOfSale?.customSettings?.showNcsaAthleteForm || hasCustomFields;

	const PURCHASE_TICKETS_STEPS = [
		{ label: 'Purchaser', name: 'EnterPurchaserDetails' },
		{ label: 'Spectator', name: 'ManageSpectators' },
		...(isShowStepOther
			? [{ label: 'Other', name: hasCustomFields ? 'AdditionalQuestionsForm' : 'NCSAForm' }]
			: []),
		{ label: 'Payment', name: 'TicketsPayment' },
	];

	return {
		isShowStepOther,
		isLoading: isLoadingCustomFields || isLoadingPointOfSale,
		PURCHASE_TICKETS_STEPS,
	};
};
