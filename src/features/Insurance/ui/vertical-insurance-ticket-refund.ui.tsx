import { useQuery } from '@tanstack/react-query';
import { TicketRefundComponent } from '@vertical-insure/web-components/react';

import { insuranceQueries } from '~api/insurance/insurance.queries';

type Props = {
	amount: number;
	eventStartDate: Date | null;
	eventEndDate: Date | null;
	customerId: string;
	customerZip: string;
};

export const VerticalInsuranceTicketRefund = (props: Props) => {
	const { data: customerClientSecret } = useQuery(
		insuranceQueries.getCustomerClientSecret(props.customerId),
	);

	if (!customerClientSecret) {
		return null;
	}

	const mapToVerticalInsuranceDate = (date: Date | null) => {
		/**
		 * Vertical insurance does not allow events that already started, so we should not pass the date
		 */
		if (!date || date <= new Date()) {
			return undefined;
		}

		return date.toISOString().substring(0, 10);
	};

	return (
		<TicketRefundComponent
			clientId={customerClientSecret.clientId}
			customerClientSecret={customerClientSecret.secret}
			customerCountry="US"
			customerPostalCode={props.customerZip}
			insurableAmount={props.amount}
			eventStartDate={mapToVerticalInsuranceDate(props.eventStartDate)}
			eventEndDate={mapToVerticalInsuranceDate(props.eventEndDate)}
			includePaymentElement
		></TicketRefundComponent>
	);
};
