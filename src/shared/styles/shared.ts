import { Box, Button, FormControlLabel, TextField } from '@mui/material';
import { styled } from 'styled-components';

import { ThemeT } from './theme';

export const ContainerStyled = styled.div``;

export const ButtonStyled = styled(Button)<ThemeT>`
	min-height: 48px;
	padding: 11px 22px !important;
	text-transform: none !important;
	font-size: 15px !important;
	line-height: 26px !important;
	min-width: 204px !important;
	background: ${(props) =>
		props.color === 'primary' && props.theme.light.colors.primary.blue} !important;
	border-radius: 8px !important;
	color: ${(props) =>
		props.variant === 'outlined' && props.theme.light.colors.button.outlined} !important;
	border: 1px solid ${(props) => (props.variant === 'outlined' ? '#ccc' : 'none')} !important;
	border: 1px solid
		${(props) => (props.variant === 'outlined' ? props.theme.light.colors.form.border : 'none')} !important;
	&.Mui-disabled {
		background-color: rgba(145, 158, 171, 0.24) !important;
		color: rgba(145, 158, 171, 0.8);
	}
`;

export const ButtonWrapper = styled(Box)<ThemeT>`
	width: 100%;
	display: flex;
	justify-content: center;
	gap: 8px;

	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		position: sticky;
		bottom: 0;
		background: #fff;
		z-index: 100;
		padding: 16px 0;
	}
	button {
		width: 100%;
	}
`;
export const Form__TextField = styled(TextField)<{ $width?: number }>`
	width: ${({ $width }) => `${$width}px` || 'auto'};
`;

export const Form__Row = styled.div<{ $mb?: number }>`
	margin-bottom: ${({ $mb }) => $mb || 0}px;
	display: flex;
	width: 100%;
	justify-content: space-between;
`;
export const Form__Wrapper = styled.div<{ $mb?: number; $width?: number }>`
	max-width: ${({ $width }) => `${$width}px` || 'auto'};
	display: flex;
	flex-direction: column;
	margin: 0 auto;
	margin-bottom: ${({ $mb }) => $mb}px;
`;
export const StyledAddNewSpectatorBtnWrapper = styled.div``;
export const StyledAddNewSpectatorBtn = styled.button<ThemeT>`
	background: transparent;
	border: none;
	cursor: pointer;
	font-size: 13px;
	padding: 0 0 0 6px;
	font-weight: 700;
	display: flex;
	align-items: center;
	margin: 0 0 50px;
	color: ${(props) => props.theme.light.colors.primary.blue};
	img {
		padding: 0 8px 0 0;
	}
`;
export const Shadow__Box = styled.div<ThemeT>`
	border-radius: 8px;
	padding: 10px;
	box-shadow: 0px 20px 40px -4px ${(props) => props.theme.light.colors.shadow.main};
`;
export const BackToTicketBtn = styled(Button)<ThemeT>`
	border-radius: 8px !important;
	background-color: #fff !important;
	border: 1px solid ${(props) => props.theme.light.colors.primary.blue} !important;
	padding: 11px 22px !important;
	font-size: 15px !important;
	color: ${(props) => props.theme.light.colors.primary.blue} !important;
	text-transform: none !important;
`;
export const Modal__Wrapper = styled.div<ThemeT>`
	width: 994px;
	max-height: 90vh;
	overflow-y: scroll;
	border-radius: 8px;
	background-color: #fff;
	box-shadow: 0px 20px 40px -4px ${(props) => props.theme.light.colors.shadow.main};
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	position: fixed;
	padding: 84px 60px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.desktopContentWidth}) {
		width: calc(100% - 40px);
	}
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		top: 130px;
		max-height: calc(100vh - 205px);
		transform: translate(-50%, 0);
		padding: 20px 22px 0 22px;
	}
`;
export const Form__Label = styled(FormControlLabel)<ThemeT>`
	span {
		font-size: 14px;
		margin-right: 15px;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
			font-size: 12px;
			margin-right: 10px;
		}
	}
	.Mui-checked {
		svg {
			fill: #4066ff;
		}
	}
	svg {
		fill: #637381;
	}
`;
export const Order__Wrapper = styled.div<ThemeT & { $isSingleTicket?: boolean }>`
	padding-top: ${(props) => (props.$isSingleTicket ? 0 : 20)}px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		padding-bottom: 70px;
	}
`;

export const StyledFixedButtonBottomWrapper = styled.div<ThemeT>`
	height: 80px;
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background: #fff;
	display: flex;
	justify-content: center;
	align-items: center;
	button {
		width: calc(100% - 40px);
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.tablet}) {
		position: static;
		height: auto;
		max-width: 432px;
		margin: 0 auto;
		button {
			width: 100%;
		}
	}
`;
