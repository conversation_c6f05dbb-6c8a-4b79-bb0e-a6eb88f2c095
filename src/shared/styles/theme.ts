export const theme = {
	light: {
		colors: {
			primary: {
				green: '#00AB55',
				blue: '#3366ff',
				red: '#ff4842',
				white: '#fff',
			},
			text: {
				primary: '#161c24',
				secondary: '#637381',
				black: '#212b36',
				white: '#fff',
				disabled: '#919EABCC',
			},
			button: {
				outlined: '#212b36',
			},
			shadow: {
				main: 'rgba(145, 158, 171, 0.16)',
			},
			form: {
				border: 'rgba(145, 158, 171, 0.32)',
			},
		},
	},
	breakpoints: {
		mobile: '375px',
		mobileS: '430px',
		tabletS: '650px',
		tablet: '768px',
		desktopLayoutWidth: '912px',
		desktopContentWidth: '872px',
		desktopModalWidth: '806px',
	},
	button: {
		contained: {
			backgroundColor: '#3366ff',
			color: '#fff',
			border: `1px solid #3366ff`,
		},
		outlined: {
			backgroundColor: 'transparent',
			color: '#212B36',
			border: `1px solid rgba(145, 158, 171, 0.32)`,
		},
		errorContained: {
			backgroundColor: '#FF4842',
			color: '#fff',
			border: `1px solid #FF4842`,
		},
		disabledContained: {
			backgroundColor: 'rgba(145, 158, 171, 0.24)',
			color: '#919EABCC',
			border: `1px solid transparent`,
		},
		disabledOutlined: {
			backgroundColor: 'transparent',
			color: '#3366ff',
			border: `1px solid #3366ff`,
		},
		default: {
			backgroundColor: '#3366ff',
			color: '#fff',
			border: `1px solid #3366ff`,
		},
	},
	zIndex: {
		eventCard: 3,
	},
};

export type ThemeT = {
	theme: typeof theme;
};
