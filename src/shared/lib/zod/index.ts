import { isValidPhoneNumber } from 'libphonenumber-js';
import { z } from 'zod';

import { CountryCode, CountryCodeKeys } from '~constants/country';

export const zodPhoneValidation = z
	.string()
	.trim()
	.refine((value) => isValidPhoneNumber(value), {
		message: 'Phone number is invalid',
	});

export const zodZipValidation = z.string().regex(/^[0-9]{5}$/);
export const isValidBahamasZip = (zip: string): boolean => {
	return /^[A-Z]{1,2}-\d{4,5}$/.test(zip);
};

export const getPhoneWithoutPrefix = (phone: string, countryCode: CountryCodeKeys) => {
	const { phonePrefix } = CountryCode[countryCode];
	return phone.replace(phonePrefix, '');
};
