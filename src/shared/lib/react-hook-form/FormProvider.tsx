import { ReactNode } from 'react';
import { Form<PERSON>rovider as RHFForm<PERSON>rovider, UseFormReturn } from 'react-hook-form';

type Props = {
	children: ReactNode;
	methods: UseFormReturn<any>;
	onSubmit: (data: any) => void;
};

export function FormProvider({ children, onSubmit, methods }: Props) {
	const handleKeyDown = (e: React.KeyboardEvent<HTMLFormElement>) => {
		if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {
			e.preventDefault();
		}
	};
	return (
		<RHFFormProvider {...methods}>
			<form onSubmit={methods.handleSubmit(onSubmit)} onKeyDown={handleKeyDown}>
				{children}
			</form>
		</RHFFormProvider>
	);
}
