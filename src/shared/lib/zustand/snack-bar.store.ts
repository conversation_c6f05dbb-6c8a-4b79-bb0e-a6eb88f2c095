import { create } from 'zustand';

import { TicketSelectionErrors } from '~enums/ticket-selection-errors';
import { TicketSelectionErrorType } from '~types/ticket-selection-errors.type';

const messages = {
	[TicketSelectionErrors.DAILY_WEEKEND_ERROR]:
		'It would not be possible to select both Daily and Weekend passes simultaneously.',
	[TicketSelectionErrors.FREE_TICKETS_WITH_NON_FREE_ERROR]:
		'Sorry, but you can buy only paid or free tickets in one purchase',
	[TicketSelectionErrors.SPECTATOR_NAME_UNIQUE]: 'First names must be unique',
	[TicketSelectionErrors.ONLY_ONE_WEEKEND_TICKET]:
		'It would not be possible to select both more than one Weekend passes for the same spectator simultaneously.',
};

export interface ISnackBarStore {
	isShowSnackbar: boolean;
	snackBarMessageType: TicketSelectionErrorType | null;
	snackBarMessage: string;
	snackBarType: 'error' | 'info';
	setSnackbar: ({
		isShowSnackbar,
		snackBarMessageType,
		snackBarType,
	}: {
		isShowSnackbar: boolean;
		snackBarMessageType?: TicketSelectionErrorType | null;
		snackBarType?: 'error' | 'info';
	}) => void;
}

const initStore: Omit<ISnackBarStore, 'setSnackbar'> = {
	isShowSnackbar: false,
	snackBarMessageType: null,
	snackBarType: 'info',
	snackBarMessage: '',
};

export const useSnackBarStore = create<ISnackBarStore>((set) => ({
	...initStore,
	setSnackbar: ({ isShowSnackbar, snackBarMessageType = null, snackBarType = 'info' }) => {
		return set({
			isShowSnackbar,
			snackBarMessageType,
			snackBarType,
			snackBarMessage: snackBarMessageType ? messages[snackBarMessageType] : '',
		});
	},
}));
