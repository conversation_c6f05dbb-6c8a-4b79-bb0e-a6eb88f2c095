import { FieldErrors } from 'react-hook-form';
import { create } from 'zustand';

import { ManageSpectatorsFormData } from '~features/PurchaseTickets/ui/ManageSpectators';

interface ISpectatorFormErrorsStore {
	errors: FieldErrors<ManageSpectatorsFormData> | null;
	setErrors: (errors: FieldErrors<ManageSpectatorsFormData>) => void;
	clearErrors: () => void;
}

export const useSpectatorFormErrorsStore = create<ISpectatorFormErrorsStore>((set) => ({
	errors: null,
	setErrors: (errors) => set({ errors }),
	clearErrors: () => set({ errors: null }),
}));
