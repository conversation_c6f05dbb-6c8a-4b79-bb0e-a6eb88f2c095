import { useLayoutEffect, useState } from 'react';

import { theme } from '~styles/theme';

type BreakpointKey = keyof typeof theme.breakpoints;

export const useViewportInfo = () => {
	const [breakPoint, setBreakPoint] = useState<BreakpointKey>();
	const [windowHeight, setWindowHeight] = useState(window.innerHeight);
	const [windowWidth, setWindowWidth] = useState(window.innerWidth);
	const mobileBreakpoints = ['tabletS', 'mobileS', 'mobile'];

	useLayoutEffect(() => {
		const getCurrentBreakpoint = () => {
			setWindowHeight(window.innerHeight);
			setWindowWidth(window.innerWidth);

			const currentWidth = window.innerWidth;
			const { breakpoints } = theme;
			let result: keyof typeof theme.breakpoints = 'desktopContentWidth';

			for (const key of Object.keys(breakpoints)) {
				const breakpoint = parseInt(breakpoints[key as BreakpointKey]);
				if (currentWidth <= breakpoint) {
					result = key as BreakpointKey;
					break;
				}
			}

			setBreakPoint(result);
		};
		getCurrentBreakpoint();

		window.addEventListener('resize', getCurrentBreakpoint);

		return () => {
			window.removeEventListener('resize', getCurrentBreakpoint);
		};
	}, []);

	const isMobile = mobileBreakpoints.includes(breakPoint || '');

	return { breakPoint, windowHeight, windowWidth, isMobile };
};
