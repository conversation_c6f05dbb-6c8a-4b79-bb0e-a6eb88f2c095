import { ThemeT } from 'shared/styles/theme';
import styled from 'styled-components';

export const STyledHeaderWrapper = styled.header<ThemeT>`
	display: flex;
	align-items: center;
	height: 64px;
	padding: 0 20px;
	background: #fff;
	margin-bottom: 24px;
	box-shadow: 0px 8px 16px ${(props) => props.theme.light.colors.shadow.main};
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 49px;
		position: sticky;
		top: 0;
		z-index: 5;
	}
`;
export const StyledHeaderLogo = styled.img``;
