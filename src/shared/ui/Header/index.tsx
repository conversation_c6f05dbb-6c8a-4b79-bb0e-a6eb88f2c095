import { Link } from 'react-router-dom';

import logo from '~assets/logo.svg';
import mobileLogo from '~assets/mobile-logo-icon.svg';
import { useViewportInfo } from '~hooks/useViewportInfo';

import { STyledHeaderWrapper, StyledHeaderLogo } from './styled';

export const Header = () => {
	const { isMobile } = useViewportInfo();

	return (
		<STyledHeaderWrapper>
			<Link to={`${import.meta.env.VITE_TICKETS_BASE_URL}/#/events`}>
				{isMobile ? (
					<StyledHeaderLogo src={mobileLogo} alt="SportWrench" />
				) : (
					<StyledHeaderLogo src={logo} alt="SportWrench" />
				)}
			</Link>
		</STyledHeaderWrapper>
	);
};
