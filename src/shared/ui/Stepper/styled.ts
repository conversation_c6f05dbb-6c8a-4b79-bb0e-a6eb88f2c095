import { Box, StepConnector, stepConnectorClasses } from '@mui/material';
import { ThemeT } from 'shared/styles/theme';
import styled from 'styled-components';

export const StyledStepperWrapper = styled(Box)`
	max-width: 680px;
	width: 100%;
	margin: 0 auto 50px;
`;
export const StyledStepperCompleted = styled(Box)<ThemeT>`
	width: 20px;
	height: 20px;
	border-radius: 100%;
	background: ${(props) => props.theme.light.colors.primary.blue};
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	svg {
		font-size: 16px;
		fill: #fff;
	}
`;

export const StyledStepperCurrent = styled(Box)<ThemeT>`
	width: 20px;
	height: 20px;
	border-radius: 100%;
	border: 1px solid ${(props) => props.theme.light.colors.primary.blue};
	color: ${(props) => props.theme.light.colors.primary.blue};
	text-align: center;
	font-size: 14px;
`;
export const StyledStepperIncomplete = styled(Box)`
	width: 20px;
	height: 20px;
	border-radius: 100%;
	background: #919eab;
	border: 1px solid #919eab;
	color: #fff;
	text-align: center;
	font-size: 14px;
`;

export const StyledQontoConnector = styled(StepConnector)(() => ({
	[`& .${stepConnectorClasses.line}`]: {
		borderColor: 'rgba(145, 158, 171, 0.24)',
		borderTopWidth: 1,
		borderRadius: 1,
	},
}));

export const StyledQontoStepIconRoot = styled.div.withConfig({
	shouldForwardProp: (prop) => prop !== 'ownerState',
})<{
	className?: string;
	ownerState: {
		active?: boolean;
	};
}>`
	display: flex;
	height: 22px;
	align-items: center;

	${(props) =>
		props.ownerState.active &&
		`
		display: flex;
		height: 22px;
		align-items: center;
	`}
`;
