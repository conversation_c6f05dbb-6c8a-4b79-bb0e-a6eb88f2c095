import DoneIcon from '@mui/icons-material/Done';
import Step from '@mui/material/Step';
import { StepIconProps } from '@mui/material/StepIcon';
import StepLabel from '@mui/material/StepLabel';
import MuiStepper from '@mui/material/Stepper';

import { useActiveStepNameStore } from '~lib/zustand/active-step-name.store.store';

import {
	StyledQontoConnector,
	StyledQontoStepIconRoot,
	StyledStepperCompleted,
	StyledStepperCurrent,
	StyledStepperIncomplete,
	StyledStepperWrapper,
} from './styled';

type Props = {
	activeStep: number;
	steps: { label: string; name: string }[];
};

function QontoStepIcon(props: StepIconProps, activeStep: number) {
	const { active, className, icon } = props;
	return (
		<StyledQontoStepIconRoot ownerState={{ active }} className={className}>
			{activeStep === icon && <StyledStepperCurrent>{icon}</StyledStepperCurrent>}
			{activeStep > Number(icon) && (
				<StyledStepperCompleted>
					<DoneIcon />
				</StyledStepperCompleted>
			)}
			{activeStep < Number(icon) && <StyledStepperIncomplete>{icon}</StyledStepperIncomplete>}
		</StyledQontoStepIconRoot>
	);
}

export const Stepper = ({ activeStep, steps }: Props) => {
	const { setActiveStepName } = useActiveStepNameStore();

	const stepperClickHandler = (componentName: string) => {
		if (componentName) {
			setActiveStepName(`${componentName}_${new Date().getTime()}`);
		}
	};
	return (
		<StyledStepperWrapper>
			<MuiStepper alternativeLabel activeStep={activeStep} connector={<StyledQontoConnector />}>
				{steps.map((step) => (
					<Step key={step.label}>
						<StepLabel
							StepIconComponent={(props) => QontoStepIcon(props, activeStep)}
							onClick={() => stepperClickHandler(step.name)}
						>
							{step.label}
						</StepLabel>
					</Step>
				))}
			</MuiStepper>
		</StyledStepperWrapper>
	);
};
