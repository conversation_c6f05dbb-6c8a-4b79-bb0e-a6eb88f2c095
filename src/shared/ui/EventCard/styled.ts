import { Box } from '@mui/material';
import styled from 'styled-components';

import { ThemeT } from '~styles/theme';

export const StyledEventCardWrapper = styled(Box)<ThemeT>`
	box-shadow: 0px 12px 24px -4px ${(props) => props.theme.light.colors.shadow.main};
	padding: 16px 24px;
	border-radius: 12px;
	margin: 0 auto 36px;
	position: relative;
	display: flex;
	align-items: center;
	background: #fff;
	z-index: ${(props) => props.theme.zIndex.eventCard};
	position: sticky;
	top: 0;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		border-radius: 8px;
		padding: 15px 54px;
		top: 43px;
	}
`;
export const StyledEventCardTitleWrapper = styled(Box)`
	text-align: center;
	width: 100%;
`;
export const StyledEventCardTitle = styled.h3<{ theme: ThemeT }>`
	font-size: 18px;
	font-weight: 700;
	line-height: 28px;
`;
export const StyledEventCardDate = styled.h4<{ theme: ThemeT }>`
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
`;
export const StyledSnackbarWrapper = styled.div`
	position: absolute;
	width: 100%;
	left: 0;
	top: 0;
`;
