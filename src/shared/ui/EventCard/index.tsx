import dayjs from 'dayjs';

import { PointOfSale } from '~api/point-of-sale/point-of-sale.types';
import { useSnackBarStore } from '~lib/zustand/snack-bar.store';
import { Snackbar } from '~ui/Snackbar';

import {
	StyledEventCardDate,
	StyledEventCardTitle,
	StyledEventCardTitleWrapper,
	StyledEventCardWrapper,
	StyledSnackbarWrapper,
} from './styled';

type Props = {
	pointOfSale: PointOfSale;
};

export const EventCard = ({ pointOfSale }: Props) => {
	const { isShowSnackbar, snackBarMessage, snackBarType } = useSnackBarStore();
	const formatDate = (isoDate: string | null, timezone?: string | null) => {
		if (!isoDate) return '';

		if (timezone) {
			return dayjs(isoDate).tz(timezone).format('MMM DD, YYYY');
		} else {
			return dayjs(isoDate).format('MMM DD, YYYY');
		}
	};

	return (
		<StyledEventCardWrapper>
			<StyledEventCardTitleWrapper>
				<StyledEventCardTitle>{pointOfSale.name}</StyledEventCardTitle>
				<StyledEventCardDate>{`${formatDate(pointOfSale.dateStart, pointOfSale.timezone)} - ${formatDate(pointOfSale.dateEnd, pointOfSale.timezone)}, ${pointOfSale.city || ''}, ${pointOfSale.state || ''}`}</StyledEventCardDate>
			</StyledEventCardTitleWrapper>
			{isShowSnackbar && (
				<StyledSnackbarWrapper>
					<Snackbar message={snackBarMessage} type={snackBarType} />
				</StyledSnackbarWrapper>
			)}
		</StyledEventCardWrapper>
	);
};
