import styled from 'styled-components';

import { SnackbarType } from './types';

export const StyledSnackbarMessage = styled.p`
	font-size: 12px;
	line-height: 18px;
`;

export const StyledSnackbarWrapper = styled.div<{ $type: SnackbarType }>`
	padding: 13px 12px 13px 16px;
	background: ${({ $type }) => ($type === 'error' ? '#FFE7D9' : '#D0F2FF')};
	display: flex;
	gap: 12px;
	align-items: center;
	border-radius: 8px;
	box-shadow: 0px 8px 16px 0px rgba(145, 158, 171, 0.16);
	${StyledSnackbarMessage} {
		color: ${(props) => (props.$type === 'error' ? '#7A0C2E' : '#04297A')};
	}
`;
