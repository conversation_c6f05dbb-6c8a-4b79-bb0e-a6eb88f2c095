import errorIcon from './img/error.icon.svg';
import infoIcon from './img/info.icon.svg';
import { StyledSnackbarMessage, StyledSnackbarWrapper } from './styled';
import { SnackbarType } from './types';

type Props = {
	message: string;
	type: SnackbarType;
};

export const Snackbar = ({ message, type }: Props) => {
	return (
		<StyledSnackbarWrapper $type={type}>
			<img src={type === 'error' ? errorIcon : infoIcon} alt="" />
			<StyledSnackbarMessage>{message}</StyledSnackbarMessage>
		</StyledSnackbarWrapper>
	);
};
