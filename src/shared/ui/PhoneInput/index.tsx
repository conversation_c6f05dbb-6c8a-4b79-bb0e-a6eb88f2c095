import { getCountryCallingCode, isValidPhoneNumber } from 'libphonenumber-js';
import { useCallback, useEffect, useState } from 'react';
import { useController, useFormContext } from 'react-hook-form';
import PhoneInputComponent from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import { z } from 'zod';

import { CountryCodeEnum, CountryCodeKeys } from '~constants/country';
import { usePrevious } from '~hooks/usePrevious';
import { getPhoneWithoutPrefix } from '~lib/zod';

import { StylesPhoneError, StylesPhoneWrapper } from './styled';

type Props = {
	name: string;
	countryCode: CountryCodeKeys;
};

const COUNTRY_CODE_FIELD = 'countryCode';
const INTERNATIONAL_NUMBER_FIELD = 'internationalNumber';
const DEFAULT_COUNTRY_CODE = CountryCodeEnum.US;

export const PhoneInputSchema = z
	.object({
		[COUNTRY_CODE_FIELD]: z.nativeEnum(CountryCodeEnum).default(DEFAULT_COUNTRY_CODE),
		[INTERNATIONAL_NUMBER_FIELD]: z.string(),
	})
	.refine(
		(data) => {
			const internationalNumber = getPhoneWithoutPrefix(
				data[INTERNATIONAL_NUMBER_FIELD],
				data.countryCode,
			);
			return (
				Boolean(data[COUNTRY_CODE_FIELD]) &&
				isValidPhoneNumber(internationalNumber, data[COUNTRY_CODE_FIELD])
			);
		},
		{
			message: 'Phone number is invalid',
			path: [INTERNATIONAL_NUMBER_FIELD],
		},
	);

export function PhoneInput({ name, countryCode }: Props) {
	const { control } = useFormContext();
	const prevCountryCode = usePrevious<CountryCodeKeys>(countryCode);
	const [isTouched, setIsTouched] = useState(false);

	const {
		field: { value: phoneValue, onChange },
		fieldState: { error },
	} = useController({
		name,
		control,
	});

	const currentCountryCode =
		phoneValue?.[COUNTRY_CODE_FIELD] || countryCode || DEFAULT_COUNTRY_CODE;
	const [selectedCountry, setSelectedCountry] = useState<CountryCodeKeys>(countryCode);

	const handleChange = useCallback(
		(value: string | undefined) => {
			onChange({
				countryCode: selectedCountry || currentCountryCode,
				internationalNumber: value || '',
			});
		},
		[onChange, currentCountryCode, selectedCountry],
	);
	const handleCountryChange = useCallback((newCountry: CountryCodeKeys) => {
		setIsTouched(false);
		setSelectedCountry(newCountry);
	}, []);

	useEffect(() => {
		if (countryCode && countryCode !== prevCountryCode) {
			setIsTouched(false);
			onChange({
				countryCode,
				internationalNumber: phoneValue?.[INTERNATIONAL_NUMBER_FIELD] || '',
			});
		}
	}, [countryCode, prevCountryCode, phoneValue, onChange]);

	useEffect(() => {
		if (
			phoneValue?.[INTERNATIONAL_NUMBER_FIELD] === '' ||
			!phoneValue?.[INTERNATIONAL_NUMBER_FIELD]?.startsWith('+')
		) {
			if (selectedCountry) {
				const prefix = `+${getCountryCallingCode(selectedCountry)}`;
				onChange({
					countryCode: selectedCountry,
					internationalNumber: prefix,
				});
			}
		}
	}, [phoneValue.INTERNATIONAL_NUMBER_FIELD, selectedCountry, onChange, phoneValue]);

	const onBlur = () => setIsTouched(true);

	return (
		<div>
			<StylesPhoneWrapper>
				<PhoneInputComponent
					international
					defaultCountry={currentCountryCode}
					value={(phoneValue?.[INTERNATIONAL_NUMBER_FIELD] || '').trim()}
					onChange={handleChange}
					onBlur={onBlur}
					countryCallingCodeEditable={false}
					countries={Object.values(CountryCodeEnum)}
					focusInputOnCountrySelection={false}
					onCountryChange={handleCountryChange}
				/>
			</StylesPhoneWrapper>
			{error && isTouched && <StylesPhoneError>Phone is invalid</StylesPhoneError>}
		</div>
	);
}
