import styled from 'styled-components';

export const StylesPhoneWrapper = styled.div`
	border-radius: 4px;
	border: 1px solid rgba(0, 0, 0, 0.23);
	display: flex;
	align-items: center;
	padding: 0 14px;
	height: 56px;
	input {
		border: none;
		outline: none;
		width: 100%;
		height: 100%;
	}
	.PhoneInput {
		height: 100%;
		width: 100%;
	}
	.PhoneInputCountrySelectArrow {
		display: none;
	}
`;
export const StylesPhoneError = styled.div`
	color: #d32f2f;
	font-size: 12px;
	margin-top: 3px;
	margin-left: 14px;
`;
