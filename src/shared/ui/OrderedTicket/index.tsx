import { Box } from '@mui/material';
import { Link } from 'react-router-dom';

import qrIcon from '~assets/qr-small-icon.svg';
import { useViewportInfo } from '~hooks/useViewportInfo';
import { OrderT } from '~types/order.type';

import { OrderedTicket_Wrapper, OrderedTicket__Name, OrderedTicket__Order } from './styled';

type PropsT = {
	order: OrderT;
	isSingleTicket?: boolean;
};
export const OrderedTicket = ({ isSingleTicket, order: { id, name, order, customer } }: PropsT) => {
	const { breakPoint } = useViewportInfo();
	const getTicketOrderName = (order: string) => {
		const orderArr = order.split(':');
		if ((breakPoint === 'tabletS' || breakPoint === 'mobileS') && orderArr.length > 1) {
			return `${orderArr[0]}: <br/> ${orderArr[1]}`;
		}
		return order;
	};
	return (
		<OrderedTicket_Wrapper $isSingleTicket={isSingleTicket}>
			<Box sx={{ display: 'flex' }}>
				<img src={qrIcon} alt="qr code" />
				<Box sx={{ marginLeft: '16px' }}>
					{customer ? (
						<OrderedTicket__Name>{customer}</OrderedTicket__Name>
					) : (
						<OrderedTicket__Name>
							<Link to={`/ticket/${id}`}>{name} </Link>
						</OrderedTicket__Name>
					)}
				</Box>
			</Box>
			{customer ? (
				<Box sx={{ display: 'flex', flexDirection: 'column', textAlign: 'right' }}>
					<OrderedTicket__Order style={{ fontSize: '16px' }}>{name}</OrderedTicket__Order>
					<OrderedTicket__Order style={{ fontSize: '14px' }}>{order}</OrderedTicket__Order>
				</Box>
			) : (
				// Danger render as html
				<OrderedTicket__Order dangerouslySetInnerHTML={{ __html: getTicketOrderName(order) }} />
			)}
		</OrderedTicket_Wrapper>
	);
};
