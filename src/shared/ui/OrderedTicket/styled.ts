import { ThemeT } from 'shared/styles/theme';
import styled from 'styled-components';

export const OrderedTicket_Wrapper = styled.div<ThemeT & { $isSingleTicket?: boolean }>`
	border-radius: 8px;
	background-color: #fff;
	box-shadow: 0px 20px 40px -4px ${(props) => props.theme.light.colors.shadow.main};
	max-width: ${(props) => (props.$isSingleTicket ? '872px' : '800px')};
	margin: 0 auto ${(props) => (props.$isSingleTicket ? '16px' : '24px')};
	padding: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		align-items: flex-start;
	}
`;
export const OrderedTicket__Name = styled.div<ThemeT>`
	font-size: 20px;
	line-height: 30px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		font-size: 16px;
		font-style: normal;
		font-weight: 400;
		line-height: 24px;
	}
	a {
		text-decoration: none;
		font-size: 20px;
		line-height: 30px;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
			font-size: 16px;
			font-style: normal;
			font-weight: 400;
			line-height: 24px;
		}
	}
`;
export const OrderedTicket__Order = styled.div<ThemeT>`
	color: ${(props) => props.theme.light.colors.text.secondary};
	font-size: 14px;
	line-height: 22px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		font-size: 12px;
		font-style: normal;
		font-weight: 400;
		line-height: 18px;
		text-align: right;
	}
`;
