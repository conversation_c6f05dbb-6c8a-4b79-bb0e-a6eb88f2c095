import { TextFieldProps } from '@mui/material';
import { Controller, useFormContext } from 'react-hook-form';

import { StyledDefaultTextField } from './styled';

type Props = TextFieldProps & {
	name: string;
};

export function Input({ name, ...other }: Props) {
	const { control } = useFormContext();

	return (
		<Controller
			name={name}
			control={control}
			render={({ field, fieldState: { error } }) => (
				<StyledDefaultTextField
					{...field}
					fullWidth
					error={!!error}
					helperText={error?.message}
					{...other}
				/>
			)}
		/>
	);
}
