import React, { useEffect, useRef, useState } from 'react';

type Props = {
	primaryText: string;
	fallbackText: string;
	maxWidth: number;
	font?: string;
};

export const SmartText: React.FC<Props> = ({
	primaryText,
	fallbackText,
	maxWidth,
	font = '14px / 50px "Public Sans"',
}) => {
	const [useFallback, setUseFallback] = useState(false);
	const canvasRef = useRef<HTMLCanvasElement | null>(null);

	useEffect(() => {
		const canvas = canvasRef.current ?? document.createElement('canvas');
		const ctx = canvas.getContext('2d');
		if (!ctx) return;

		ctx.font = font;
		const textWidth = ctx.measureText(primaryText).width;
		setUseFallback(textWidth > maxWidth);
	}, [primaryText, maxWidth, font]);

	return (
		<>
			{useFallback ? fallbackText : primaryText}
			<canvas ref={canvasRef} style={{ display: 'none' }} />
		</>
	);
};
