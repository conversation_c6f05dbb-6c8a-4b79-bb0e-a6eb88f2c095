import { Box } from '@mui/material';
import { Link } from 'react-router-dom';

import qrIcon from '~assets/qr-small-icon.svg';
import shareIcon from '~assets/share-icon.svg';
import { OrderT } from '~types/order.type';

import { OrderedItem__Name, OrderedItem__Order, OrderedItem__Wrapper } from './styled';

type PropsT = {
	order: OrderT;
};
export const OrderedItem = ({ order: { id, name, order } }: PropsT) => {
	return (
		<OrderedItem__Wrapper>
			<Box sx={{ display: 'flex' }}>
				<img src={qrIcon} alt="qr code" />
				<Box sx={{ marginLeft: '16px' }}>
					<OrderedItem__Name>
						<Link to={`/orders/${id}`}>{name}</Link>
					</OrderedItem__Name>
					<OrderedItem__Order>{order}</OrderedItem__Order>
				</Box>
			</Box>
			<Link to="/">
				<img src={shareIcon} alt="share" />
			</Link>
		</OrderedItem__Wrapper>
	);
};
