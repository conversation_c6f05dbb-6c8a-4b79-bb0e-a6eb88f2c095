import { TextField, TextFieldProps } from '@mui/material';
import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';

type Props = TextFieldProps & {
	name: string;
	children: React.ReactNode;
};

export function Select({ name, children, ...other }: Props) {
	const { control } = useFormContext();

	return (
		<Controller
			name={name}
			control={control}
			render={({ field, fieldState: { error } }) => (
				<TextField
					{...field}
					select
					fullWidth
					SelectProps={{ native: true }}
					error={!!error}
					helperText={error?.message}
					{...other}
				>
					{children}
				</TextField>
			)}
		/>
	);
}
