import ClipLoader from 'react-spinners/ClipLoader';

import { StyledButton } from './styled';
import { Color, Size, Variant } from './types';

type Props = {
	onClick?: (e: React.MouseEvent) => void;
	label: string;
	fullWidth?: boolean;
	disabled?: boolean;
	variant?: Variant;
	color?: Color;
	size?: Size;
	type?: 'button' | 'submit' | 'reset';
	isLoading?: boolean;
};

export const Button = ({
	onClick,
	label,
	disabled,
	variant,
	color,
	size,
	type,
	fullWidth,
	isLoading = false,
}: Props) => {
	return (
		<StyledButton
			onClick={onClick}
			disabled={!!disabled || isLoading}
			$variant={variant}
			$color={color}
			$size={size}
			type={type}
			$fullWidth={fullWidth}
		>
			{!isLoading && label}
			{isLoading && <ClipLoader />}
		</StyledButton>
	);
};
