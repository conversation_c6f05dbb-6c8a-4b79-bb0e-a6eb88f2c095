import styled from 'styled-components';

import { ThemeT } from '~styles/theme';

import { Color, Size, Variant } from './types';

export const StyledButton = styled.button<
	ThemeT & {
		theme: ThemeT;
		$variant?: Variant;
		$color?: Color;
		$size?: Size;
		$fullWidth?: boolean;
		disabled: boolean;
	}
>`
	display: inline-flex;         
	align-items: center;          
	justify-content: center;  
	font-weight: 700;
	border-radius: 8px;
	cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
	border: ${(props) =>
		props.disabled
			? props.theme.button.disabledContained.border
			: props.theme.button[props.$variant || 'default'].border};
	background: ${(props) =>
		props.disabled
			? props.theme.button.disabledContained.backgroundColor
			: props.theme.button[props.$variant || 'default'].backgroundColor};
	color: ${(props) =>
		props.disabled
			? props.theme.button.disabledContained.color
			: props.theme.button[props.$variant || 'default'].color};
	height: ${(props) => (props.$size === 'small' ? '36px' : '48px')};
	width: ${(props) => (props.$fullWidth ? '100%' : 'auto')};
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		/* mobile */
		font-size: 14px;
		padding: 6px 16px;
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.tablet}) {
		/* desktop */
		font-size: ${(props) => (props.$size === 'small' ? '14px' : '15px')};
		padding: ${(props) => (props.$size === 'small' ? '6px 16px' : '11px 22px')};
	}
`;
