export const CountryCode = {
	US: {
		value: 'United States',
		text: 'United States',
		phonePrefix: '+1',
		stripe: 'US',
		zipCode: 'xxxxx',
	},
	CA: { value: 'Canada', text: 'Canada', phonePrefix: '+1', stripe: 'CA', zipCode: 'A1A 1A1' },
	AS: {
		value: 'American Samoa',
		text: 'American Samoa',
		phonePrefix: '+1',
		stripe: 'AS',
		zipCode: 'xxxxx',
	},
	BS: { value: 'Bahamas', text: 'Bahamas', phonePrefix: '+1', stripe: 'BS', zipCode: 'xxxxx' },
	BM: { value: 'Bermuda', text: 'Bermuda', phonePrefix: '+1', stripe: 'BM', zipCode: 'AA 11' },
	DO: {
		value: 'Dominican Republic',
		text: 'Dominican Republic',
		phonePrefix: '+1',
		stripe: 'DO',
		zipCode: 'xxxxx',
	},
	GU: { value: 'Guam', text: 'Guam', phonePrefix: '+1', stripe: 'GU', zipCode: 'xxxxx' },
	MX: { value: 'Mexico', text: 'Mexico', phonePrefix: '+52', stripe: 'MX', zipCode: 'xxxxx' },
	PR: {
		value: 'Puerto Rico',
		text: 'Puerto Rico',
		phonePrefix: '+1',
		stripe: 'PR',
		zipCode: 'xxxxx',
	},
	VI: {
		value: 'Virgin Islands',
		text: 'Virgin Islands',
		phonePrefix: '+1',
		stripe: 'VI',
		zipCode: 'xxxxx',
	},
	UA: { value: 'Ukraine', text: 'Ukraine', phonePrefix: '+380', stripe: 'UA', zipCode: 'xxxxx' },
} as const;

export type CountryCodeKeys = keyof typeof CountryCode;

export const CountryCodeEnum = Object.keys(CountryCode).reduce(
	(acc, key) => {
		acc[key as CountryCodeKeys] = key as CountryCodeKeys;
		return acc;
	},
	{} as Record<CountryCodeKeys, CountryCodeKeys>,
) as {
	[K in CountryCodeKeys]: K;
};
