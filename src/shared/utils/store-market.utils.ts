import { UAParser } from 'ua-parser-js';

export const getStoreLink = () => {
	const parser = new UAParser();
	const result = parser.getResult();

	const ANDROID_LINK = import.meta.env.VITE_ANDROID_STORE as string;
	const IOS_LINK = import.meta.env.VITE_IOS_STORE as string;

	const os = result.os.name?.toLowerCase();

	if (os?.includes('android') || os?.includes('windows')) {
		return ANDROID_LINK;
	} else if (os?.includes('ios')) {
		return IOS_LINK;
	} else if (os?.includes('macos')) {
		return IOS_LINK;
	}

	return '#';
};
