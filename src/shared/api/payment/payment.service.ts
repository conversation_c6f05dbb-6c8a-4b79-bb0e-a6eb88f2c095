import { apiClient, validateResponse } from '../api-client';
import { PaymentSchema } from './payment.schemas';

export class PaymentService {
	static getBySessionId(paymentSessionId: string, signal?: AbortSignal) {
		return apiClient
			.get(`/payments/session/${paymentSessionId}`, {
				signal,
				customMetadata: { disableErrorNotification: true },
			})
			.then(validateResponse(PaymentSchema));
	}
}
