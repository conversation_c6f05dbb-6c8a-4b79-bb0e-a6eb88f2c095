import { z } from 'zod';

import { PaymentProviderType } from '~api/payment-session/payment-session.types';

import { PaymentStatus, PaymentType } from './payment.types';

export const PaymentSchema = z.object({
	id: z.string().describe('ID of the payment'),
	orderId: z.string().nullable().describe('ID of the order (nullable)'),
	paymentIdAtProvider: z.string().describe('ID of the payment at the provider'),
	initialAmount: z.number().describe('Initial payment amount'),
	currentAmount: z.number().describe('Current payment amount'),
	status: z.nativeEnum(PaymentStatus).describe('Status of the payment'),
	type: z.nativeEnum(PaymentType).describe('Type of payment'),
	paymentProviderType: z.nativeEnum(PaymentProviderType).describe('Payment provider type'),
	totalFeeAmount: z.number().describe('Total fee amount'),
	netAmount: z.number().describe('Net amount after fees'),
	paymentProviderFee: z.number().describe('Payment provider fee'),
	marketplaceFee: z.number().describe('Marketplace fee'),
});
