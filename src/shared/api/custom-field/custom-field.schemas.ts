import { z } from 'zod';

import { CustomFieldType } from './custom-field.types';

const CustomFieldOptionSchema = z.object({
	key: z.string().describe('Custom Field option key'),
	value: z.string().describe('Custom Field option value'),
});

export const CustomFieldSchema = z.object({
	id: z.string().describe('ID of the custom field'),
	type: z.nativeEnum(CustomFieldType).describe('Type of the custom field'),
	label: z.string().describe('Label of the custom field'),
	shortLabel: z.string().nullable().describe('Custom Field Short Label'),
	required: z.boolean().describe('Indicates if field is required'),
	isHidden: z.boolean().describe('Indicates if custom field is hidden'),
	order: z.number().describe('Order of custom field'),
	options: z.array(CustomFieldOptionSchema).nullable(),
});

export const CustomFieldListSchema = z.array(CustomFieldSchema);
