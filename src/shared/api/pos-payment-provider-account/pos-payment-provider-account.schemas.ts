import { z } from 'zod';

import { FeePayer } from '~api/payment-session/payment-session.types';

export const PosPaymentProviderAccountSchema = z.object({
	id: z.string().describe('ID of the event'),
	pointOfSaleId: z.string().describe('ID of the point of sale'),
	paymentProviderAccountId: z.string().describe('ID of the payment provider account'),
	statementDescriptor: z.string().nullable().optional().describe('ID at provider'),
	paymentProviderFeeFixed: z.number().describe('Payment provider fee fixed'),
	paymentProviderFeePercentage: z.number().describe('Payment provider fee percentage'),
	paymentProviderFeePayer: z.nativeEnum(FeePayer).describe('Payment provider fee payer'),
});
