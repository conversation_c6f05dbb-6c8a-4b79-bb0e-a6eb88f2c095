import { queryOptions } from "@tanstack/react-query";
import { PosPaymentProviderAccountService } from "./pos-payment-provider-account.service";

export const posPaymentProviderAccountQueries = {
  getByPosId: (posId: string) =>
    queryOptions({
      queryKey: ["posPaymentProviderAccount", posId],
      queryFn: ({ signal }) => PosPaymentProviderAccountService.getByPosId(posId, signal),
      staleTime: 120000,
    }),
};
