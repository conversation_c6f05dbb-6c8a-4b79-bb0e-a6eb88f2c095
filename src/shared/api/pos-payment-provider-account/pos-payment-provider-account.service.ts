import { apiClient, validateResponse } from '../api-client';
import { PosPaymentProviderAccountSchema } from './pos-payment-provider-account.schemas';

export class PosPaymentProviderAccountService {
	static getByPosId(posId: string, signal?: AbortSignal) {
		return apiClient
			.get(`/point-of-sales/${posId}/payment-provider-accounts/active`, { signal })
			.then(validateResponse(PosPaymentProviderAccountSchema));
	}
}
