import { apiClient } from '../api-client';

export class BanService {
	static checkBan(fingerprint: string, email: string, posId: string, signal?: AbortSignal) {
		return apiClient.post(
			`/point-of-sales/${posId}/bans/check`,
			{ fingerprint, email },
			{ signal, customMetadata: { disableErrorNotification: true } },
		);
	}

	static checkBanByFingerprint(
		fingerprint: string,
		posId: string,
		signal?: AbortSignal,
	) {
		return apiClient.post(
			`/point-of-sales/${posId}/bans/check`,
			{ fingerprint },
			{ signal, customMetadata: { disableErrorNotification: true } },
		);
	}

	static checkBanByEmail(
		email: string,
		posId: string,
		signal?: AbortSignal,
	) {
		return apiClient.post(
			`/point-of-sales/${posId}/bans/check`,
			{ email },
			{ signal, customMetadata: { disableErrorNotification: true } },
		);
	}
}
