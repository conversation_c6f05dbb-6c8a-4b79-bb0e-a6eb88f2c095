import { z } from 'zod';

import { <PERSON>e<PERSON>ayer } from '~api/payment-session/payment-session.types';
import { Gender } from '~enums/gender';

export const EventSchema = z.object({
	event_kiosk_description: z.string().optional(),
	stripe_key: z.string(),
	plaid_key: z.string(),
	date_start: z.string(),
	date_end: z.string(),
	city: z.string(),
	state: z.string(),
	name: z.string(),
	description: z.string(),
	additional_fields: z.any(),
	count_service_fee: z.boolean(),
	stripe_percent: z.number(),
	stripe_fixed: z.number(),
	stripe_fee_payer: z.nativeEnum(FeePayer),
	sw_fee_payer: z.nativeEnum(FeePayer),
	has_payment_discount: z.boolean(),
	require_tickets_names: z.boolean(),
	not_require_sw_fee_for_checks: z.boolean(),
	event_id: z.number(),
	small_logo: z.string(),
	date_start_with_time: z.string(),
	date_end_with_time: z.string(),
	require_ticket_coupon: z.boolean(),
	show_ncsa_athlete_form: z.boolean(),
	showcase_registration: z.boolean(),
	event_ticket_buy_entry_code_required: z.boolean(),
	max_allowed_tickets_in_purchase: z.number(),
	// custom_forms_requires_submitting: z.boolean(),
});

export const ncsaAthleteSubmitSchema = z.object({
	athlete_first: z.string().min(1, 'First name is required'),
	athlete_last: z.string().min(1, 'Last name is required'),
	athlete_gradyear: z
		.number()
		.int('Graduation year must be an integer')
		.min(1900, 'Graduation year must be valid'),
	gender: z.enum([Gender.FEMALE, Gender.MALE]),
	parent_email: z.string().email('Must be a valid email address'),
	parent_first: z.string().min(1, 'First name is required'),
	parent_last: z.string().min(1, 'Last name is required'),
	parent_phone: z.string().min(10, 'Phone number must be at least 10 digits'),
	zip: z
		.string()
		.min(5, 'ZIP code must be at least 5 characters')
		.max(10, 'ZIP code cannot exceed 10 characters'),
});
