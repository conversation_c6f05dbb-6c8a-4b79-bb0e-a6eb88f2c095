import { swApiClient, validateRequest } from '../api-client';
import { ncsaAthleteSubmitSchema } from './event.schemas';
import { NcsaAthleteSubmitDto } from './event.types';

export class EventService {
	static submitNcsa(posId: string, athleteData: NcsaAthleteSubmitDto) {
		return swApiClient.post(
			`/ncsa/submit/point-of-sales/${posId}/athlete`,
			validateRequest(ncsaAthleteSubmitSchema, athleteData),
		);
	}
}
