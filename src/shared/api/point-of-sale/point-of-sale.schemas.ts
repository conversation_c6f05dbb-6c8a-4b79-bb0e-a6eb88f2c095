import { z } from 'zod';

import { <PERSON><PERSON><PERSON>ayer } from '~api/payment-session/payment-session.types.ts';

const PointOfSaleSchema = z.object({
	id: z.string().describe('ID of the point of sale'),
	name: z.string().describe('Name of the point of sale'),
	shortName: z.string().describe('Short name of the point of sale'),
	marketplaceFee: z.number().describe('Marketplace fee'),
	marketplaceFeePayer: z.nativeEnum(FeePayer).describe('Marketplace fee payer'),
	marketplaceId: z.string().describe('Marketplace ID'),
	dateStart: z.string().nullable().describe('Start date of the event'),
	dateEnd: z.string().nullable().describe('End date of the event'),
	salesStartDate: z.string().nullable().describe('Start date of the event sales'),
	salesEndDate: z.string().nullable().describe('End date of the event sales'),
	published: z.boolean().describe('Published status of the event'),
	description: z.string().nullable().describe('Description of the event'),
	disclaimer: z.string().nullable().describe('Disclaimer for the event'),
	timezone: z.string().describe('Timezone of the event'),
	location: z.array(z.string()).nullable().describe('Locations of the event'),
	website: z.string().nullable().describe('Website of the event'),
	city: z.string().nullable().describe('City where the event is held'),
	email: z.string().email().nullable().describe('Contact email for the event'),
	phone: z.string().nullable().describe('Contact phone number for the event'),
	country: z.string().nullable().describe('Country where the event is held'),
	address: z.string().nullable().describe('Address of the event'),
	state: z.string().nullable().describe('State of the event'),
	zip: z.string().nullable().describe('ZIP code of the event location'),
	customSettings: z
		.object({
			showNcsaAthleteForm: z.boolean().optional().describe('Show NCSA athlete form'),
		})
		.describe('Custom settings'),
	entryCodeRequired: z.boolean().describe('Entry code required'),
});

const ValidateEntryCodeSchema = z.object({
	entryCode: z.string().min(1, 'Entry code cannot be empty'),
});

const ValidateEntryCodeResponseSchema = z.object({
	isValid: z.boolean().describe('Is entry code valid'),
});

export { PointOfSaleSchema, ValidateEntryCodeSchema, ValidateEntryCodeResponseSchema };
