import { apiClient, validateRequest, validateResponse } from '../api-client';
import {
	PointOfSaleSchema,
	ValidateEntryCodeResponseSchema,
	ValidateEntryCodeSchema,
} from './point-of-sale.schemas';

export class PointOfSaleService {
	static getById(posId: string, signal?: AbortSignal) {
		return apiClient
			.get(`/point-of-sales/${posId}`, { signal })
			.then(validateResponse(PointOfSaleSchema));
	}

	static validateEntryCode({ posId, code }: { posId: string; code: string }, signal?: AbortSignal) {
		return apiClient
			.post(
				`/point-of-sales/${posId}/validate-entry-code`,
				validateRequest(ValidateEntryCodeSchema, { entryCode: code }),
				{ signal },
			)
			.then(validateResponse(ValidateEntryCodeResponseSchema));
	}
}
