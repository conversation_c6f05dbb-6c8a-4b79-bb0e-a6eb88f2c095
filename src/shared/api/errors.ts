export class ApiDomainError {
	code: ErrorCodes;
	message: string;
	status: number;
	constructor(code: string, message: string, status: number) {
		this.code = code as ErrorCodes;
		this.message = message;
		this.status = status;
	}
}

export enum ErrorCodes {
	FINGERPRINT_BANNED = 'BAN.FINGERPRINT_BANNED',
	EMAIL_BANNED = 'BAN.EMAIL_BANNED',
	CANT_HAVE_FREE_AND_NON_FREE_PRODUCTS = 'ORDER.CANT_HAVE_FREE_AND_NON_FREE_PRODUCTS',
}
