import { z } from 'zod';

export const ProductSchema = z.object({
	id: z.string().describe('ID of the event'),
	pointOfSaleId: z.string().describe('ID of point of sale'),
	description: z.string().nullable().optional().describe('Product Description'),
	label: z.string().describe('Product Label'),
	shortLabel: z.string().nullable().describe('Product Short Label'),
	initialPrice: z.number().describe('Product Initial Price'),
	currentPrice: z.number().describe('Product Current Price'),
	marketplaceFee: z.number().describe('Product Marketplace Fee'),
	ticketType: z.enum(['daily', 'weekend']).describe('Ticket Type (daily or weekend)'),
	createdAt: z.string().describe('Product Created At'),
});

export const ProductListSchema = z.array(ProductSchema);
