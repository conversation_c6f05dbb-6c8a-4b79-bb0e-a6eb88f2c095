import { z } from 'zod';

import {
	CreatePaymentSessionDtoSchema,
	PaymentSessionSchema,
	UpdatePaymentSessionDtoSchema,
} from './payment-session.schemas';

export type PaymentSession = z.infer<typeof PaymentSessionSchema>;

export type CreatePaymentSessionDto = z.infer<typeof CreatePaymentSessionDtoSchema>;

export type UpdatePaymentSessionDto = z.infer<typeof UpdatePaymentSessionDtoSchema>;

export enum FeePayer {
	BUYER = 'buyer',
	SELLER = 'seller',
}

export enum PaymentProviderType {
	PAYMENT_HUB = 'payment-hub',
}

export enum PaymentProviderPaymentType {
	CARD = 'card',
	ACH = 'ach',
	APPLE_PAY = 'apple_pay',
	GOOGLE_PAY = 'google_pay',
}
