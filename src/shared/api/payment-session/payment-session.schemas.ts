import { z } from 'zod';

import { FeePayer, PaymentProviderPaymentType, PaymentProviderType } from './payment-session.types';

export const PaymentSessionSchema = z.object({
	id: z.string().describe('ID of the payment'),
	amount: z.number().positive().describe('Payment amount'),
	marketplaceFee: z.number().describe('Marketplace fee'),
	paymentFee: z.number().describe('Payment fee'),
	marketplaceFeePayer: z.nativeEnum(FeePayer).describe('Marketplace fee payer'),
	paymentFeePayer: z.nativeEnum(FeePayer).describe('Payment fee payer'),
	orderId: z.string().describe('ID of the order'),
	paymentIntentIdAtProvider: z.string().describe('ID of provider payment'),
	paymentProviderType: z.nativeEnum(PaymentProviderType).describe('Payment provider type'),
	paymentProviderPaymentType: z
		.nativeEnum(PaymentProviderPaymentType)
		.describe('Payment provider payment type'),
});

export const CreatePaymentSessionDtoSchema = z.object({
	orderId: z.string(),
	paymentProviderPaymentType: z.nativeEnum(PaymentProviderPaymentType),
	total: z.number().positive().describe('Payment total'),
	marketplaceFee: z.number().describe('Marketplace fee'),
	paymentFee: z.number().describe('Payment fee'),
});

export const UpdatePaymentSessionDtoSchema = z.object({
	paymentProviderPaymentType: z.nativeEnum(PaymentProviderPaymentType),
});
