import { apiClient, validateRequest, validateResponse } from '../api-client';
import {
	CreatePaymentSessionDtoSchema,
	PaymentSessionSchema,
	UpdatePaymentSessionDtoSchema,
} from './payment-session.schemas';
import { CreatePaymentSessionDto, UpdatePaymentSessionDto } from './payment-session.types';

export class PaymentSessionService {
	static create(dto: CreatePaymentSessionDto, signal?: AbortSignal) {
		return apiClient
			.post(`/payment-sessions`, validateRequest(CreatePaymentSessionDtoSchema, dto), { signal })
			.then(validateResponse(PaymentSessionSchema));
	}

	static update(paymentSessionId: string, dto: UpdatePaymentSessionDto, signal?: AbortSignal) {
		return apiClient
			.put(
				`/payment-sessions/${paymentSessionId}`,
				validateRequest(UpdatePaymentSessionDtoSchema, dto),
				{ signal },
			)
			.then(validateResponse(PaymentSessionSchema));
	}

	static getById(paymentSessionId: string, signal?: AbortSignal) {
		return apiClient
			.get(`/payment-sessions/${paymentSessionId}`, { signal })
			.then(validateResponse(PaymentSessionSchema));
	}
}
