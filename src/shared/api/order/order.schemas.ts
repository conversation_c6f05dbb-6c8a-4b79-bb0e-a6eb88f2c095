import { z } from 'zod';

import { zodPhoneValidation, zodZipValidation } from '~lib/zod';

import { OrderStatus } from './order.types';

const OrderItemHolderSchema = z.object({
	firstName: z.string().nullable().optional(),
	lastName: z.string().nullable().optional(),
	address: z.string().nullable().optional(),
	zip: zodZipValidation.nullable().optional(),
	phone: zodPhoneValidation.nullable().optional(),
	email: z.string().email({ message: 'Invalid email address' }).nullable().optional(),
	country: z.string().nullable().optional(),
	state: z.string().nullable().optional(),
});

const OrderItemSchema = z.object({
	id: z.string().describe('ID of the order item'),
	price: z.number().describe('Order item price'),
	quantity: z.number().describe('Order item quantity'),
	productId: z.string().describe('ID of product'),
	marketplaceFee: z.number().describe('Marketplace Fee'),
	holder: OrderItemHolderSchema,
});

export const OrderSchema = z.object({
	id: z.string().describe('ID of the order'),
	pointOfSaleId: z.string().nullable().describe('ID of point of sale'),
	customerId: z.string().describe('ID of customer'),
	amount: z.number().describe('Order amount'),
	status: z.nativeEnum(OrderStatus).describe('Order status'),
	orderItems: z.array(OrderItemSchema).min(1).describe('List of order items'),
});

const CreateCustomerDtoSchema = z.object({
	firstName: z.string().min(1, { message: 'First name is required' }),
	lastName: z.string().min(1, { message: 'Last name is required' }),
	zip: z.string().min(1, { message: 'ZIP code is required' }).nullable().optional(),
	phone: zodPhoneValidation,
	country: z.string().transform((value) => value.toLowerCase()),
	email: z.string().email({ message: 'Invalid email address' }),
});

const CreateOrderItemDtoSchema = z.object({
	quantity: z.number().min(1, { message: 'Quantity must be at least 1' }).nonnegative(),
	productId: z.string(),
	holder: OrderItemHolderSchema.nullable().optional(),
});

const OrderCustomField = z.object({
	customFieldId: z.string(),
	value: z.string().min(1, 'Value is required'),
});

export const CreateOrderDtoSchema = z.object({
	amount: z.number(),
	pointOfSaleId: z.string(),
	orderItems: z.array(CreateOrderItemDtoSchema),
	marketplaceFee: z.number(),
	customer: CreateCustomerDtoSchema,
	customFieldResponses: z.array(OrderCustomField),
	entryCode: z.string().min(1, 'Entry code cannot be empty').nullable(),
});
