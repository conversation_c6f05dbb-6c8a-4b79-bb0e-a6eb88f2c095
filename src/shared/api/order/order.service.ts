import { apiClient, validateRequest, validateResponse } from '../api-client';
import { CreateOrderDtoSchema, OrderSchema } from './order.schemas';
import { CreateOrderDto } from './order.types';

export class OrderService {
	static create(dto: CreateOrderDto, signal?: AbortSignal) {
		return apiClient
			.post(`/orders`, validateRequest(CreateOrderDtoSchema, dto), {
				signal,
				headers: {
					'Content-Type': 'application/json',
				},
				customMetadata: { disableErrorNotification: true },
			})
			.then(validateResponse(OrderSchema));
	}

	static getById(orderId: string, signal?: AbortSignal) {
		return apiClient.get(`/orders/${orderId}`, { signal }).then(validateResponse(OrderSchema));
	}

	static markAsPaid(orderId: string, signal?: AbortSignal) {
		return apiClient
			.put(`/orders/${orderId}/mark-paid`, null, { signal })
			.then(validateResponse(OrderSchema));
	}
}
