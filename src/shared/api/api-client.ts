import axios, {
	AxiosError,
	AxiosInstance,
	AxiosResponse,
	CreateAxiosDefaults,
	InternalAxiosRequestConfig,
	isAxiosError,
} from 'axios';
import { toast } from 'react-toastify';
import { ZodIssue, ZodType } from 'zod';

import { ApiDomainError } from './errors';

const SALES_HUB_API_HOST = import.meta.env.VITE_SALES_HUB_API_HOST as string;
const SW_API_HOST = import.meta.env.VITE_SW_API_HOST as string;

const VITE_USE_PROXY = import.meta.env.VITE_USE_PROXY === 'true';

type ApiErrorResponse = { code: string; message: string; status: number };

function isApiDomainError(error: unknown): error is AxiosError<ApiErrorResponse> {
	return (
		isAxiosError(error) &&
		error.response?.data &&
		typeof error.response.data.code === 'string' &&
		typeof error.response.data.status === 'number'
	);
}

const CANCELED_ERROR_NAME = 'CanceledError';

const shouldSkipErrorNotification = (error: AxiosError<{ message: string }>) => {
	const skipConditions = [
		error.config?.customMetadata?.disableErrorNotification,
		CANCELED_ERROR_NAME === error.name,
	];

	return skipConditions.some(Boolean);
};

const createErrorInterceptor = (client: AxiosInstance) =>
	client.interceptors.response.use(undefined, (error: AxiosError<{ message: string }>) => {
		const errorMessage = error.response?.data?.message || error.message;

		if (!shouldSkipErrorNotification(error)) {
			toast.error(errorMessage);
		}

		console.error('Request error:', error);
		if (error.response && isApiDomainError(error)) {
			return Promise.reject(
				new ApiDomainError(
					error.response.data.code,
					error.response.data.message,
					error.response.data.status,
				),
			);
		}

		return Promise.reject(error);
	});

const createAxiosInstance = (config: CreateAxiosDefaults) => {
	const client = axios.create(config);

	createErrorInterceptor(client);

	return client;
};

export const apiClient = createAxiosInstance({
	baseURL: VITE_USE_PROXY ? '/api' : SALES_HUB_API_HOST,
});

export const swApiClient = createAxiosInstance({
	baseURL: VITE_USE_PROXY ? '/api-sw' : SW_API_HOST,
});

export const validateResponse = <Data>(schema: ZodType<Data>) => {
	return (response: AxiosResponse<unknown>): Data => {
		const validation = schema.safeParse(response.data);

		if (!validation.success) {
			toast('Received invalid format from Server', { type: 'warning' });
			console.error('Validation of response from server failed', validation.error);
		}

		if (validation.error) {
			throw new AxiosValidationError(
				response.config,
				response.request,
				response,
				validation.error.errors,
			);
		}

		return validation.data;
	};
};

export const validateRequest = <Data>(schema: ZodType<Data>, data: unknown) => {
	const validation = schema.safeParse(data);

	if (validation.error) {
		toast('Invalid request data format', { type: 'warning' });
		console.error('Invalidate request data format', { data, error: validation.error });
		throw new Error('Validation of request data failed');
	}

	return validation.data;
};

export class AxiosValidationError<T = unknown, D = unknown> extends AxiosError {
	static readonly ERR_BAD_VALIDATION = 'ERR_BAD_VALIDATION';

	constructor(
		config?: InternalAxiosRequestConfig<D>,
		request?: unknown,
		response?: AxiosResponse<T, D>,
		readonly errors?: ZodIssue[],
	) {
		super(
			'The provided data does not meet the required criteria.',
			AxiosValidationError.ERR_BAD_VALIDATION,
			config,
			request,
			response,
		);
	}
}
