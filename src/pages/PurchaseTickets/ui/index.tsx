import { PurchaseTickets } from '~features/PurchaseTickets/ui';
import { usePurchaseSettings } from '~features/PurchaseTickets/usePurchaseSettings';
import { ContainerStyled } from '~styles/shared';
import { EventCard } from '~ui/EventCard';

export const PurchaseTicketsPage = () => {
	const { pointOfSale } = usePurchaseSettings();

	return (
		<ContainerStyled>
			{pointOfSale && <EventCard pointOfSale={pointOfSale} />}
			<PurchaseTickets />
		</ContainerStyled>
	);
};
