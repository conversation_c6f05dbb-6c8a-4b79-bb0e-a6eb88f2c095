import { Box } from '@mui/material';
import { Link, useNavigate, useParams } from 'react-router-dom';

import { usePurchaseSettings } from '~features/PurchaseTickets/usePurchaseSettings';
import { BackToTicketBtn, ContainerStyled, Order__Wrapper } from '~styles/shared';
import { EventCard } from '~ui/EventCard';
import { OrderedTicket } from '~ui/OrderedTicket';

import { orders } from '../model/data';
import { Order__Footer, Order__TitleName } from './styled';

export const OrderWidget = () => {
	const navigate = useNavigate();

	const { posId } = useParams();
	const { pointOfSale } = usePurchaseSettings();
	return (
		<ContainerStyled>
			{pointOfSale && <EventCard pointOfSale={pointOfSale} />}
			<Order__Wrapper>
				<Order__TitleName><PERSON></Order__TitleName>
				{orders.map((order) => (
					<OrderedTicket order={order} key={order.id} />
				))}
				<Order__Footer>
					<p>
						For questions about your tickets, contact <Link to={'/'}>event support</Link>
					</p>
					<p>
						To view your purchases visit <Link to={'/'}>Sportwrench Ticketing</Link>
					</p>
				</Order__Footer>
				<Box sx={{ display: 'flex', justifyContent: 'center' }}>
					<BackToTicketBtn
						onClick={() => navigate(`/point-of-sales/${posId}/orders`)}
						variant="outlined"
					>
						Back to ticket page
					</BackToTicketBtn>
				</Box>
			</Order__Wrapper>
		</ContainerStyled>
	);
};
