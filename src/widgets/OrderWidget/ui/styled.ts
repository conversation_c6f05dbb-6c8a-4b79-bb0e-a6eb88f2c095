import { ThemeT } from 'shared/styles/theme';
import styled from 'styled-components';

export const Order__Wrapper = styled.div<{ $isSingleTicket?: boolean }>`
	padding-top: ${(props) => (props.$isSingleTicket ? 0 : 20)}px;
`;
export const Order__TitleName = styled.p<ThemeT>`
	font-size: 32px;
	line-height: 48px;
	text-align: center;
	font-weight: 700;
	margin: 0 0 35px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		font-size: 20px;
		font-style: normal;
		font-weight: 700;
		line-height: 30px;
	}
`;
export const Order__Footer = styled.footer<ThemeT>`
	padding: 15px 0 0;
	margin: 0 auto 87px;
	max-width: 800px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		position: fixed;
		bottom: 0;
		left: 24px;
		background: ${(props) => props.theme.light.colors.primary.white};
		padding: 16px 0;
		width: calc(100% - 48px);
		margin: 0;
		z-index: 9;
	}
	p {
		line-height: 24px;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			line-height: 18px;
		}
	}
	a {
		color: ${(props) => props.theme.light.colors.primary.blue};
		text-decoration: none;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			line-height: 18px;
		}
	}
`;
