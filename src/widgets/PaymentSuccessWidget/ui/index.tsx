import { Link, Navigate, useLocation } from 'react-router-dom';

import { Order } from '~api/order/order.types';
import paymentSuccessLogo from '~assets/payment-success-logo.svg';
import { PurchaserDetailsFormData } from '~features/PurchaseTickets/ui/EnterPurchasesrDetails';
import { usePurchaseSettings } from '~features/PurchaseTickets/usePurchaseSettings';
import { ContainerStyled } from '~styles/shared';
import { EventCard } from '~ui/EventCard';
import { OrderedItem } from '~ui/OrderedItem';
import { formatCents } from '~utils/number.utils';

import { orders } from '../model/data';
import {
	PaymentSuccess__Description,
	PaymentSuccess__Footer,
	PaymentSuccess__Header,
	PaymentSuccess__SubDescription,
	PaymentSuccess__Title,
	PaymentSuccess__Wrapper,
} from './styled';

export const PaymentSuccessWidget = () => {
	const location = useLocation();
	const { order, customer } = (location.state || {}) as {
		order?: Order;
		customer?: PurchaserDetailsFormData;
	};

	const { posId, pointOfSale } = usePurchaseSettings();

	if (!order || !customer) {
		return <Navigate to={`/point-of-sales/${posId}/purchase-tickets`} />;
	}

	return (
		<ContainerStyled>
			{pointOfSale && <EventCard pointOfSale={pointOfSale} />}
			<PaymentSuccess__Wrapper>
				<PaymentSuccess__Header>
					<img src={paymentSuccessLogo} alt="payment success" />
					<PaymentSuccess__Title>
						Payment of ${formatCents(order.amount)} was successful.
					</PaymentSuccess__Title>
					<PaymentSuccess__Description>
						A link to your tickets has been sent to {customer.email} from sportwrench.com.
					</PaymentSuccess__Description>
					<PaymentSuccess__SubDescription>
						Click a name to view related tickets, share them, or add them to your digital wallet
					</PaymentSuccess__SubDescription>
				</PaymentSuccess__Header>
				{orders.map((order) => (
					<OrderedItem order={order} key={order.id} />
				))}
				<PaymentSuccess__Footer>
					<p>
						For questions about your tickets, contact <Link to={'/'}>event support</Link>
					</p>
					<p>
						To view your purchases visit <Link to={'/'}>Sportwrench Ticketing</Link>
					</p>
				</PaymentSuccess__Footer>
			</PaymentSuccess__Wrapper>
		</ContainerStyled>
	);
};
