import { ThemeT } from 'shared/styles/theme';
import styled from 'styled-components';

export const PaymentSuccess__Wrapper = styled.div<ThemeT>`
	padding: 80px 0;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		padding: 40px 0;
	}
`;
export const PaymentSuccess__Header = styled.header<ThemeT>`
	width: 640px;
	margin: 0 auto 80px;
	text-align: center;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		width: 100%;
		img {
			width: 60px;
			height: 60px;
		}
	}
`;
export const PaymentSuccess__Title = styled.h1<ThemeT>`
	font-size: 32px;
	line-height: 48px;
	margin: 64px 0 24px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		font-size: 16px;
		line-height: 24px;
		margin-bottom: 6px;
		margin-top: 16px;
	}
`;
export const PaymentSuccess__Description = styled.p<ThemeT>`
	line-height: 24px;
	font-weight: 600;
	margin: 0 0 16px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		font-size: 12px;
		font-style: normal;
		line-height: 18px;
	}
`;
export const PaymentSuccess__SubDescription = styled.p<ThemeT>`
	line-height: 24px;
	margin: 0 0 16px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		font-size: 12px;
		font-style: normal;
		line-height: 18px;
	}
`;
export const PaymentSuccess__Footer = styled.footer<ThemeT>`
	max-width: 800px;
	margin: 0 auto;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		position: fixed;
		bottom: 0;
		left: 24px;
		background: ${(props) => props.theme.light.colors.primary.white};
		padding: 16px 0;
		width: calc(100% - 48px);
	}
	p {
		line-height: 24px;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
			font-size: 12px;
			font-style: normal;
			line-height: 18px;
		}
	}
	a {
		color: ${(props) => props.theme.light.colors.primary.blue};
		text-decoration: none;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
			font-size: 12px;
			font-style: normal;
			line-height: 18px;
		}
	}
`;
