import { ThemeT } from 'shared/styles/theme';
import styled from 'styled-components';

export const Orders__Wrapper = styled.div``;

export const Orders__Footer = styled.footer<ThemeT>`
	max-width: 800px;
	margin: 0 auto;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		position: fixed;
		bottom: 0;
		left: 24px;
		background: ${(props) => props.theme.light.colors.primary.white};
		padding: 16px 0;
		width: calc(100% - 48px);
	}
	p {
		line-height: 24px;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
			font-size: 12px;
			font-style: normal;
			line-height: 18px;
		}
	}
	a {
		color: ${(props) => props.theme.light.colors.primary.blue};
		text-decoration: none;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
			font-size: 12px;
			font-style: normal;
			line-height: 18px;
		}
	}
`;
