import { Link } from 'react-router-dom';

import { usePurchaseSettings } from '~features/PurchaseTickets/usePurchaseSettings';
import { ContainerStyled } from '~styles/shared';
import { EventCard } from '~ui/EventCard';
import { OrderedItem } from '~ui/OrderedItem';

import { orders } from '../model/data';
import { Orders__Footer, Orders__Wrapper } from './styled';

export const OrdersWidget = () => {
	const { pointOfSale } = usePurchaseSettings();
	return (
		<ContainerStyled>
			{pointOfSale && <EventCard pointOfSale={pointOfSale} />}
			<Orders__Wrapper>
				{orders.map((order) => (
					<OrderedItem order={order} key={order.id} />
				))}
				<Orders__Footer>
					<p>
						For questions about your tickets, contact <Link to={'/'}>event support</Link>
					</p>
					<p>
						To view your purchases visit <Link to={'/'}>Sportwrench Ticketing</Link>
					</p>
				</Orders__Footer>
			</Orders__Wrapper>
		</ContainerStyled>
	);
};
