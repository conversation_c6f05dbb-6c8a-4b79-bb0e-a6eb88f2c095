import { v4 as uuidv4 } from 'uuid';

import { Order__Wrapper } from '~styles/shared';
import { OrderedTicket } from '~ui/OrderedTicket';

import { RelatedTickets__Title, RelatedTickets__Wrapper } from './styled';

type PropsT = {
	isSingleTicket?: boolean;
};
export const RelatedTickets = ({ isSingleTicket }: PropsT) => {
	const orders = [
		{
			id: uuidv4(),
			name: '1 Daily pass',
			customer: '<PERSON>',
			order: 'Available one of the days: 09/15 - 09/16',
		},
		{
			id: uuidv4(),
			name: '1 Weekly pass',
			customer: '<PERSON>',
			order: 'Available :All days',
		},
	];

	return (
		<RelatedTickets__Wrapper>
			<RelatedTickets__Title>Related tickets</RelatedTickets__Title>
			<Order__Wrapper $isSingleTicket={isSingleTicket}>
				{orders.map((order) => (
					<OrderedTicket order={order} key={order.id} isSingleTicket={isSingleTicket} />
				))}
			</Order__Wrapper>
		</RelatedTickets__Wrapper>
	);
};
