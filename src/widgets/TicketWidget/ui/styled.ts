import styled from 'styled-components';
import { ThemeT } from '~styles/theme';

export const Ticket__Wrapper = styled.div<ThemeT>`
	padding: 70px 0 0;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		padding: 0;
	}
`;
export const Ticket__Footer = styled.footer`
	button {
		display: flex;
		margin: auto;
	}
`;
export const Ticket__FooterTitle = styled.p<ThemeT>`
	font-size: 20px;
	font-weight: 700;
	line-height: 30px;
	margin-bottom: 16px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		font-size: 16px;
		font-style: normal;
		font-weight: 600;
		line-height: 24px;
	}
`;
export const Ticket__FooterDescription = styled.p<ThemeT>`
	font-size: 16px;
	font-weight: 400;
	line-height: 24px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		&,
		a {
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			line-height: 18px;
		}
	}
`;
export const Ticket__FooterList = styled.ul`
	margin: 0 0 64px 20px;
`;
export const Ticket__FooterListItem = styled.li<ThemeT>`
	line-height: 24px;
	font-weight: 400;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.tablet}) {
		font-size: 12px;
		font-style: normal;
		font-weight: 400;
		line-height: 18px;
	}
`;
