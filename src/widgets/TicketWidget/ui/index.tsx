import { Link, useNavigate, useParams } from 'react-router-dom';

import { BackToTicketBtn, ContainerStyled } from '~styles/shared';

// import { EventCard } from '~ui/EventCard';
import { RelatedTickets } from './RelatedTickets';
import { TicketDescription } from './TicketDescription';
import {
	Ticket__Footer,
	Ticket__FooterDescription,
	Ticket__FooterList,
	Ticket__FooterListItem,
	Ticket__FooterTitle,
	Ticket__Wrapper,
} from './styled';

export const TicketWidget = () => {
	const navigate = useNavigate();
	const { posId } = useParams();

	return (
		<ContainerStyled>
			{/* <EventCard /> */}
			<TicketDescription />
			<RelatedTickets isSingleTicket />
			<Ticket__Wrapper>
				<Ticket__Footer>
					<Ticket__FooterTitle>Important information</Ticket__FooterTitle>
					<Ticket__FooterDescription>
						Complete Ticket Info & Spectator Policies can be found on our{' '}
						<Link to={'/'}>Spectators Admission</Link> page <br /> Important Information
					</Ticket__FooterDescription>
					<Ticket__FooterList>
						<Ticket__FooterListItem>Required for entry by each spectator</Ticket__FooterListItem>
						<Ticket__FooterListItem>"QR code" ticket on a mobile device.</Ticket__FooterListItem>
						<Ticket__FooterListItem>Photo ID for each person 16 and older</Ticket__FooterListItem>
						<Ticket__FooterListItem>
							If under 16, must be accompanied by an adult
						</Ticket__FooterListItem>
						<Ticket__FooterListItem>
							Entry access for spectators will begin at
						</Ticket__FooterListItem>
						<Ticket__FooterListItem>7:00am for AM WAVE 1 Spectators</Ticket__FooterListItem>
					</Ticket__FooterList>
					<BackToTicketBtn
						onClick={() => navigate(`/point-of-sales/${posId}/orders`)}
						variant="outlined"
					>
						Back to ticket page
					</BackToTicketBtn>
				</Ticket__Footer>
			</Ticket__Wrapper>
		</ContainerStyled>
	);
};
