import { RouterProvider as ReactRouterProvider, createBrowserRouter } from 'react-router-dom';

import { MainLayout } from '~layouts/MainLayout';
import { Order } from '~pages/Order';
import { Orders } from '~pages/Orders';
import { PaymentSuccess } from '~pages/PaymentSuccess';
import { PurchaseTicketsPage } from '~pages/PurchaseTickets';
import { Ticket } from '~pages/Ticket';

const router = createBrowserRouter([
	{
		path: '/',
		element: <MainLayout />,
		children: [
			{
				path: 'point-of-sales/:posId/purchase-tickets',
				children: [
					{
						path: '',
						element: <PurchaseTicketsPage />,
					},
					{
						path: 'orders',
						element: <Orders />,
					},
					{
						path: 'orders/:id',
						element: <Order />,
					},
					{
						path: 'payment-success',
						element: <PaymentSuccess />,
					},
					{
						path: 'ticket/:id',
						element: <Ticket />,
					},
				],
			},
		],
	},
]);

export const RouterProvider = () => {
	return <ReactRouterProvider router={router} />;
};
