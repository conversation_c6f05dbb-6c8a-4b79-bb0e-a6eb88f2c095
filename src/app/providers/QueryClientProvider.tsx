import {
	QueryClient,
	QueryClientProvider as TanStackQueryClientProvider,
} from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { AxiosError } from 'axios';
import { ReactNode } from 'react';

type QueryClientProviderProps = {
	children: ReactNode;
};

export const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			retry: false,
			refetchOnWindowFocus: false,
			staleTime: 1000 * 60 * 3,
		},
	},
});

export function QueryClientProvider(props: QueryClientProviderProps) {
	const { children } = props;

	return (
		<TanStackQueryClientProvider client={queryClient}>
			{children}
			<ReactQueryDevtools
				initialIsOpen={false}
				buttonPosition="bottom-left"
				errorTypes={[
					{
						name: 'Error',
						initializer: () => new Error('Error message'),
					},
					{
						name: 'Axios Error',
						initializer: () => new AxiosError('Axios error'),
					},
				]}
			/>
		</TanStackQueryClientProvider>
	);
}
