import React, { ReactNode } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { ThemeProvider } from 'styled-components';

import { GlobalStyle } from '~styles/globalStyles';
import { theme } from '~styles/theme';

import { RouterProvider } from '../routes';
import { QueryClientProvider } from './QueryClientProvider';

type Props = {
	children?: ReactNode;
};

export const Providers: React.FC<Props> = ({ children }) => (
	<QueryClientProvider>
		<ThemeProvider theme={theme}>
			<GlobalStyle theme={theme} />
			<RouterProvider />
			{children}
		</ThemeProvider>
		<ToastContainer />
	</QueryClientProvider>
);
