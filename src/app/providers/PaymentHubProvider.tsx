import { FormChangeEvent, PaymentForm, PaymentHub } from '@rn-web/payment-hub-sdk';
import { RefObject, createContext, useContext, useState } from 'react';

import { PaymentSession } from '~api/payment-session/payment-session.types';
import { PaymentService } from '~api/payment/payment.service';
import { PaymentStatus } from '~api/payment/payment.types';

const paymentHub = new PaymentHub({
	publishableKey: import.meta.env.VITE_PAYMENT_HUB_PUBLIC_KEY as string,
	apiHost: import.meta.env.VITE_PAYMENT_HUB_API_HOST as string,
});

type InitFormParams = {
	paymentSession: PaymentSession;
	containerElementRef: RefObject<HTMLElement>;
	onChange: (event: FormChangeEvent) => void;
};

type PaymentHubContextType = {
	confirmPayment: () => Promise<void>;
	initForm: (params: InitFormParams) => void;
	getFingerprint: () => Promise<string | null>;
	formInitialized: boolean;
};

type PaymentData = {
	paymentHubForm: PaymentForm;
	paymentSession: PaymentSession;
};

const PaymentHubProviderContext = createContext<PaymentHubContextType | undefined>(undefined);

const INVALID_FLOW_MESSAGE =
	'Something went wrong! Check the list of payments in a ' +
	'few minutes and retry the payment if it is not successful!';

export const PaymentHubProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
	const [paymentFormData, setPaymentFormData] = useState<PaymentData | null>(null);

	const waitUntilPaymentSettles = async (paymentSessionId: string): Promise<boolean> => {
		const maxAttempts = 10;
		const intervalTime = 3000; // 3 seconds
		let attempts = 0;

		return new Promise((resolve, reject) => {
			const intervalId = setInterval(async () => {
				attempts += 1;

				const paymentDetails = await PaymentService.getBySessionId(paymentSessionId).catch(
					console.error,
				);

				if (paymentDetails && paymentDetails.status === PaymentStatus.SUCCEEDED) {
					clearInterval(intervalId);
					resolve(true);
				} else if (attempts >= maxAttempts) {
					clearInterval(intervalId);
					reject(new Error(INVALID_FLOW_MESSAGE));
				}
			}, intervalTime);
		});
	};

	const confirmPayment = async () => {
		if (!paymentFormData) {
			throw new Error('Payment form is not initialized');
		}

		const { error: errorMessage } = await paymentFormData.paymentHubForm.confirmPayment();

		if (errorMessage) {
			throw new Error(errorMessage);
		}

		await waitUntilPaymentSettles(paymentFormData.paymentSession.id);
	};

	const getFingerprint = async () => {
		if (paymentFormData) {
			return paymentFormData.paymentHubForm.getFingerprint();
		}

		throw new Error('Payment form is not initialized');
	};

	const shouldSkipFormInit = (
		paymentFormData: PaymentData | null,
		paymentSession: PaymentSession,
	): paymentFormData is PaymentData => {
		return (
			paymentFormData !== null &&
			paymentFormData.paymentSession.id === paymentSession.id &&
			paymentFormData.paymentSession.amount === paymentSession.amount
		);
	};

	const initForm = async ({ paymentSession, containerElementRef, onChange }: InitFormParams) => {
		if (shouldSkipFormInit(paymentFormData, paymentSession)) {
			setPaymentFormData({ ...paymentFormData, paymentSession });
			return;
		}

		const containerElementId = `payment-form-container-${Math.random().toString(36)}`;

		if (containerElementRef.current) {
			containerElementRef.current.id = containerElementId;

			const paymentHubForm = await paymentHub.createForm(paymentSession.paymentIntentIdAtProvider, {
				containerElementId,
				onChange,
				amount: paymentSession.amount,
			});

			setPaymentFormData({ paymentHubForm, paymentSession });
		}
	};

	return (
		<PaymentHubProviderContext.Provider
			value={{ initForm, confirmPayment, getFingerprint, formInitialized: !!paymentFormData }}
		>
			{children}
		</PaymentHubProviderContext.Provider>
	);
};

export const usePaymentHubForm = () => {
	const context = useContext(PaymentHubProviderContext);

	if (context === undefined) {
		throw new Error('usePaymentHubForm must be used within a PaymentFormProvider');
	}

	return context;
};
