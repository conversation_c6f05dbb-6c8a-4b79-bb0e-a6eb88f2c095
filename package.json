{"name": "sw-tickets-frontend", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=20.16.x", "npm": "10.x"}, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^3.9.0", "@mui/icons-material": "^5.16.6", "@mui/material": "^5.16.6", "@mui/styled-engine-sc": "^6.0.0-alpha.18", "@rn-web/payment-hub-sdk": "^1.10.0", "@tanstack/react-query": "^5.51.21", "@tanstack/react-query-devtools": "^5.51.21", "@vertical-insure/web-components": "^4.15.0", "axios": "^1.7.3", "dayjs": "^1.11.13", "framer-motion": "^11.3.19", "libphonenumber-js": "^1.11.11", "lodash": "^4.17.21", "mui-tel-input": "^5.1.2", "postcode-validator": "^3.10.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.1", "react-phone-number-input": "^3.4.12", "react-query": "^3.39.3", "react-router-dom": "^6.25.1", "react-spinners": "^0.15.0", "react-toastify": "^10.0.5", "styled-components": "^6.1.12", "ua-parser-js": "^2.0.3", "uuid": "^10.0.0", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.51.15", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/lodash": "^4.17.7", "@types/node": "^22.0.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/ua-parser-js": "^0.7.39", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-react": "^4.3.1", "babel-plugin-styled-components": "^2.1.4", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "prettier": "^3.2.5", "typescript": "^5.2.2", "vite": "^5.3.4"}}